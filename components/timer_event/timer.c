/**
 * @file timer.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief
 * @version 3.0.0
 * @date 2024-11-15
 *
 * @ingroup timer_event
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include <clock/clockchip.h>
#include <clock/clocksource.h>
#include <cpuid.h>
#include <errno.h>
#include <inttypes.h>
#include <stdio.h>
#include <string.h>
#include <timer_event/timer.h>
#include <ttos.h>
#include <ttos_pic.h>

#include <time/ktime.h>

#define KLOG_TAG "timer_event"
#include <klog.h>

/** Control structure for Timer Subsystem */
struct timer_local_ctrl
{
    struct timecounter tc;
    struct clockchip *cc;
    bool started;
    bool inprocess;
    u64 next_event;
    struct timer_event *curr;
    ttos_spinlock_t event_list_lock;
    struct list_head event_list;
};

struct timer_local_ctrl tlc[CONFIG_MAX_CPUS];
static struct timecounter ref_tc;

/**
 * @brief 获取运行timer的cpu id
 *
 * 获取运行timer的cpu id
 *
 * @return 返回0，timer永远运行在0核
 * @retval 0 成功。
 */
int timer_cpuid_get(void)
{
    return cpuid_get();
}

/**
 * @brief 获取当前定时器时钟源的频率
 *
 * 获取当前定时器时钟源的频率
 *
 * @return 时钟源频率
 * @retval 时钟源频率
 */
u32 timer_clocksource_frequency(void)
{
    return timecounter_clocksource_frequency(&tlc[timer_cpuid_get()].tc);
}

/**
 * @brief 获取当前定时器时钟源的计数
 *
 * 获取当前定时器时钟源的计数
 *
 * @return 时钟源计数
 * @retval 时钟源计数
 */
u64 timer_clocksource_count(void)
{
    return timecounter_clocksource_count(&tlc[timer_cpuid_get()].tc);
}

/**
 * @brief 获取当前时钟芯片的频率
 *
 * 获取当前时钟芯片的频率
 *
 * @return 时钟芯片频率
 * @retval 时钟芯片频率
 */
u32 timer_clockchip_frequency(void)
{
    return clockchip_frequency(tlc[timer_cpuid_get()].cc);
}

/**
 * @brief 将时钟周期转换为ns
 *
 * 该函数实现时钟周期转换为ns的功能
 *
 * @param[in] cycles 时钟周期
 * @return 成功时返回时钟周期对应的ns
 * @retval 时钟周期对应的ns
 */
u64 timer_cycles_to_ns(u64 cycles)
{
    irq_flags_t flags;
    u64 nanosecs;

    struct timecounter *tc = &(tlc[timer_cpuid_get()].tc);

    arch_cpu_int_save(flags);
    nanosecs = clocksource_delta2nsecs(cycles & tc->cs->mask, tc->cs->mult, tc->cs->shift);
    arch_cpu_int_restore(flags);

    return nanosecs;
}

/**
 * @brief 将两个时钟周期的差值转换为ns
 *
 * 该函数实现传入的时钟周期与当前的时钟的差值转换为ns的功能
 *
 * @param[in] cycles 时钟周期
 * @return 成功时返回两个时钟周期的差值对应的ns
 * @retval 两个时钟周期的差值对应的ns
 */
u64 timer_delta_cycles_to_ns(u64 cycles)
{
    irq_flags_t flags;
    u64 ns_delta, cycles_now, cycles_delta;
    struct timecounter *tc = &tlc[timer_cpuid_get()].tc;

    arch_cpu_int_save(flags);
    cycles_now = tc->cs->read(tc->cs);
    if (cycles > cycles_now)
        cycles_delta = (cycles - cycles_now) & tc->cs->mask;
    else
        cycles_delta = 0;
    ns_delta = clocksource_delta2nsecs(cycles_delta, tc->cs->mult, tc->cs->shift);
    arch_cpu_int_restore(flags);

    return ns_delta;
}

/**
 * @brief 获取当前系统的时间戳
 *
 * 该函数实现获取当前系统的时间戳，时间戳以ns为单位
 *
 * @return 成功时返回当前系统的时间戳，时间戳以ns为单位
 * @retval 当前系统的时间戳
 */
u64 timer_timestamp(void)
{
    u64 ret;
    irq_flags_t flags;

    arch_cpu_int_save(flags);
    ret = timestamp_ns_get(&tlc[timer_cpuid_get()].tc);
    arch_cpu_int_restore(flags);

    return ret;
}

/* Note: This function must be called with tlcp->event_list_lock held. */
static void __timer_schedule_next_event(struct timer_local_ctrl *tlcp)
{
    u64 tstamp;
    struct timer_event *e;

    /* If not started yet or still processing events then we give up */

    // if ((tlcp->started == false) || (tlcp->inprocess == true))
    if (tlcp->started == false)
    {
        return;
    }

    /* If no events, we give up */
    if (list_empty(&tlcp->event_list))
    {
        return;
    }

    /* Retrieve first event from list of active events */
    e = list_entry(list_first(&tlcp->event_list), struct timer_event, active_head);

    /* Configure clockevent device for first event */
    tlcp->curr = e;
    tstamp = timer_timestamp();

    /* 若该函数从timer_clockchip_event_handler中的e->handler(e)中调用过来，则不用设置硬件，
    因为timer_clockchip_event_handler会反复遍历并设置,否则就会多设置一次 */
    if (tlcp->inprocess != true)
    {
        tlcp->next_event = e->expiry_tstamp;
        clockchip_program_event(tlcp->cc, tstamp, e->expiry_tstamp);
    }
}

/* Note: This function must be called with ev->active_lock held. */
static void __timer_event_stop(struct timer_event *ev)
{
    irq_flags_t flags;
    struct timer_local_ctrl *tlcp;

    if (!ev->active_state)
    {
        return;
    }

    tlcp = &tlc[ev->active_hcpu];

    write_lock_irqsave_lite(&tlcp->event_list_lock, flags);

    ev->active_state = false;
    list_del(&ev->active_head);
    ev->expiry_tstamp = 0;

    write_unlock_irqrestore_lite(&tlcp->event_list_lock, flags);
}

static void timer_clockchip_event_handler(struct clockchip *cc)
{
    u32 add_to_tail;
    u64 time_e;
    irq_flags_t flags, flags1;
    struct timer_event *e;
    struct timer_event *ev;
    struct timer_local_ctrl *tlcp = &tlc[timer_cpuid_get()];

    read_lock_irqsave_lite(&tlcp->event_list_lock, flags);

    tlcp->inprocess = true;

    /* Process expired active events */
    while (!list_empty(&tlcp->event_list))
    {
        e = list_entry(list_first(&tlcp->event_list), struct timer_event, active_head);

        /* Current timestamp */
        if (e->expiry_tstamp <= timer_timestamp())
        {
            /* Unlock event list for processing expired event */

            read_unlock_irqrestore_lite(&tlcp->event_list_lock, flags);
            /* Set current CPU event to NULL */
            tlcp->curr = NULL;
            /* Stop expired active event */

            if (e->repeat_count)
            {
                if (e->repeat_count != TTOS_WAIT_FOREVER)
                    --e->repeat_count;

                e->expiry_tstamp += e->interval_nsecs;

                add_to_tail = 0;
                list_for_each_entry(ev, &tlcp->event_list, active_head)
                {
                    if (ev->expiry_tstamp > e->expiry_tstamp)
                    {
                        add_to_tail = 1;
                        list_delete(&e->active_head);
                        list_add_before(&e->active_head, &ev->active_head);
                        break;
                    }
                }

                if (!add_to_tail)
                {
                    list_delete(&e->active_head);
                    list_add_tail(&e->active_head, &ev->active_head);
                }
            }
            else
            {
                spin_lock_irqsave_lite(&e->active_lock, flags1);
                __timer_event_stop(e);
                spin_unlock_irqrestore_lite(&e->active_lock, flags1);
            }

            /* Call event handler */
            time_e = timer_timestamp();
            if (e->handler)
                e->handler(e);
            time_e = timer_timestamp() - time_e;

            if (time_e > (u64)(100 * 1000000ULL))
            {
                KLOG_I("time_e handler: %" PRIu64 " ms\n", time_e / (u64)(1000000ULL));
            }

            /* Lock back event list */
            read_lock_irqsave_lite(&tlcp->event_list_lock, flags);
        }
        else
        {
            /* No more expired events */
            break;
        }
    }

    tlcp->inprocess = false;

    /* Schedule next timer event */
    __timer_schedule_next_event(tlcp);

    read_unlock_irqrestore_lite(&tlcp->event_list_lock, flags);
}

/**
 * @brief 获取timer event的挂起状态
 *
 * 该函数获取timer event的挂起状态
 *
 * @param[in] ev 指向timer_event的结构体指针
 * @return 成功时返回 true，失败时返回false
 * @retval true    timer event的状态为挂起
 * @retval false   参数为空或者timer event的状态为运行
 */
bool timer_event_pending(struct timer_event *ev)
{
    bool ret;
    irq_flags_t flags;

    if (!ev)
    {
        return false;
    }

    spin_lock_irqsave_lite(&ev->active_lock, flags);
    ret = ev->active_state;
    spin_unlock_irqrestore_lite(&ev->active_lock, flags);

    return ret;
}

/**
 * @brief 获取timer event的过期时间
 *
 * 该函数获取timer event的过期时间
 *
 * @param[in] ev 指向timer_event的结构体指针
 * @return 成功时返回过期时间，失败时返回0
 * @retval 过期时间
 * @retval 0, 失败
 */
u64 timer_event_expiry_time(struct timer_event *ev)
{
    u64 exp_time;
    irq_flags_t flags;

    if (!ev)
    {
        return 0;
    }

    spin_lock_irqsave_lite(&ev->active_lock, flags);
    exp_time = ev->expiry_tstamp;
    spin_unlock_irqrestore_lite(&ev->active_lock, flags);

    return exp_time;
}

/**
 * @brief 启动一个定时器事件
 *
 * 该函数用于启动一个定时器事件
 *
 * @param[in] ev 指向timer_event的结构体指针
 * @param[in] duration_nsecs 持续时间
 * @param[out] ret_expiry_tstamp 过期时间
 * @return 成功时返回0，失败时返回-1
 * @retval 0 成功。
 * @retval -1 ev参数无效
 */
int timer_event_start2(struct timer_event *ev, u64 duration_nsecs, u64 *ret_expiry_tstamp)
{
    u32 cpuid;
    u64 tstamp;
    bool found_pos = false;
    irq_flags_t flags, flags1;
    struct timer_event *e = NULL;
    struct timer_local_ctrl *tlcp;

    if (!ev)
    {
        return -1;
    }

    cpuid = timer_cpuid_get();
    tlcp = &tlc[cpuid];
    tstamp = timer_timestamp();

    spin_lock_irqsave_lite(&ev->active_lock, flags);

    __timer_event_stop(ev);

    if (!ev->expiry_tstamp)
    {
        ev->expiry_tstamp = tstamp + duration_nsecs;
    }

    if (ev->repeat_count && (ev->repeat_count != TTOS_WAIT_FOREVER))
    {
        ev->repeat_count--;
    }

    ev->duration_nsecs = duration_nsecs;
    ev->active_state = true;
    ev->active_hcpu = cpuid;

    if (ret_expiry_tstamp)
    {
        *ret_expiry_tstamp = ev->expiry_tstamp;
    }

    write_lock_irqsave_lite(&tlcp->event_list_lock, flags1);

    list_for_each_entry(e, &tlcp->event_list, active_head)
    {
        if (ev->expiry_tstamp < e->expiry_tstamp)
        {
            found_pos = true;
            break;
        }
    }

    if (!found_pos)
    {
        list_add_tail(&ev->active_head, &tlcp->event_list);
    }
    else
    {
        // list_add_tail (&ev->active_head, &e->active_head);
        list_add_before(&ev->active_head, &e->active_head);
    }

    __timer_schedule_next_event(tlcp);

    write_unlock_irqrestore_lite(&tlcp->event_list_lock, flags1);

    spin_unlock_irqrestore_lite(&ev->active_lock, flags);

    return 0;
}

/**
 * @brief 停止一个定时器事件
 *
 * 该函数用于停止一个定时器事件
 *
 * @param[in] ev 指向timer_event的结构体指针
 * @return 成功时返回0，失败时返回-1
 * @retval 0 成功。
 * @retval -1 ev参数无效
 */
int timer_event_stop(struct timer_event *ev)
{
    irq_flags_t flags;

    if (!ev)
    {
        return -1;
    }

    spin_lock_irqsave_lite(&ev->active_lock, flags);

    __timer_event_stop(ev);

    spin_unlock_irqrestore_lite(&ev->active_lock, flags);

    return 0;
}

/**
 * @brief 启动一个定时器
 *
 * 该函数用于启动一个定时器
 *
 */
void timer_start(void)
{
    u64 tstamp;
    struct timer_local_ctrl *tlcp = &tlc[timer_cpuid_get()];

    clockchip_set_mode(tlcp->cc, CLOCKCHIP_MODE_ONESHOT);

    tstamp = timer_timestamp();

    tlcp->next_event = tstamp + tlcp->cc->min_delta_ns;

    tlcp->started = true;

    clockchip_program_event(tlcp->cc, tstamp, tlcp->next_event);
}

/**
 * @brief 停止一个定时器
 *
 * 该函数用于停止一个定时器
 *
 */
void timer_stop(void)
{
    struct timer_local_ctrl *tlcp = &tlc[timer_cpuid_get()];

    clockchip_set_mode(tlcp->cc, CLOCKCHIP_MODE_SHUTDOWN);

    tlcp->started = false;
}

/**
 * @brief 配置一个周期性触发的定时器事件
 *
 * 该函数用于配置一个周期性触发的定时器事件
 *
 * @param[in] ev 指向timer_event的结构体指针
 * @param[in] repeat_count 周期性触发次数
 * @param[in] interval_nsecs 定时器触发的时间间隔
 * @return 成功时返回0，失败时返回-1
 * @retval 0 成功。
 * @retval -1 参数无效
 */
int timer_event_periodic_config(struct timer_event *ev, u32 repeat_count, u64 interval_nsecs)
{
    irq_flags_t flags;

    if (!ev || !ev->active_state)
        return -1;

    if (!(interval_nsecs && repeat_count))
        return -1;

    spin_lock_irqsave_lite(&ev->active_lock, flags);

    ev->interval_nsecs = interval_nsecs;
    ev->repeat_count = repeat_count;

    spin_unlock_irqrestore(&ev->active_lock, flags);

    return 0;
}

static int timer_startup(u32 cpu)
{
    int rc;
    u32 cpuid;
    struct timer_local_ctrl *tlcp;

    cpuid = timer_cpuid_get();

    tlcp = &tlc[cpuid];

    /* Clear timer control structure */
    memset(tlcp, 0, sizeof(*tlcp));

    /* Initialize Per CPU event status */
    tlcp->started = false;
    tlcp->inprocess = false;

    /* Initialize Per CPU current event pointer */
    tlcp->curr = NULL;

    /* Initialize Per CPU event list */
    INIT_SPIN_LOCK(&tlcp->event_list_lock);
    INIT_LIST_HEAD(&tlcp->event_list);

    /* Bind suitable clockchip to current host CPU */
    tlcp->cc = clockchip_bind_best(cpu);
    if (!tlcp->cc)
    {
        KLOG_E("%s: No clockchip for CPU%d\n", __func__, cpu);
        return ENODEV;
    }

    /* Update event handler of clockchip */
    clockchip_set_event_handler(tlcp->cc, &timer_clockchip_event_handler);

    /* Initialize timecounter wrapper of secondary CPUs
     * such that time stamps visible on all CPUs is same;
     */
    if ((rc = timecounter_init(&tlcp->tc, ref_tc.cs, timestamp_ns_get(&ref_tc))))
    {
        return rc;
    }

    return 0;
}

/**
 * @brief 定时器初始化
 *
 * 该函数用于初始化一个定时器
 *
 * @return 成功时返回0，失败时返回错误码。
 * @retval 0 成功。
 * @retval -1 失败。
 * @retval -ENODEV 未查找到设备
 */
int sys_timer_init(void)
{
    int rc = 0;

    struct timer_clocksource *cs;

    if (is_bootcpu())
    {
        /* Find suitable clocksource */
        if (!(cs = clocksource_best()))
        {
            KLOG_E("%s: No clocksource found\n", __func__);
            return -ENODEV;
        }

        /* Initialize reference timecounter wrapper */
        if ((rc = timecounter_init(&ref_tc, cs, 0)))
        {
            return rc;
        }

        /* Start reference timecounter */
        rc = timecounter_start(&ref_tc);
    }

    timer_startup(timer_cpuid_get());

    return rc;
}
