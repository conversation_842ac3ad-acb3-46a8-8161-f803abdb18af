/**
 * @file clock_timespec_max64.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief
 * @version 3.0.0
 * @date 2024-11-18
 *
 * @ingroup clock
 *
 * @since 3.0.0
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include <time/ktime.h>

/**
 * @brief 将两个timespec64结构表示的时间比较,返回更大的一个
 *
 * 该函数将两个timespec64结构表示的时间比较,返回更大的一个
 *
 * @param[in] ts1 指向第一个timespec64结构体的指针
 * @param[in] ts2 指向第二个timespec64结构体的指针
 * @return ts1和ts2更大的一个
 * @retval ts1和ts2更大的一个
 */
struct timespec64 clock_timespec_max64(const struct timespec64 *ts1, const struct timespec64 *ts2)
{
    if (ts1->tv_sec < ts2->tv_sec)
    {
        return *ts2;
    }
    else if (ts1->tv_sec == ts2->tv_sec && ts1->tv_nsec <= ts2->tv_nsec)
    {
        return *ts2;
    }
    return *ts1;
}
