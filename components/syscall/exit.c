/**
 * @file exit.c
 * <AUTHOR> (z<PERSON><PERSON><PERSON>@kyland.com)
 * @brief 终止进程
 * @version 3.0.0
 * @date 2024-11-14
 *
 * @ingroup syscall
 *
 * @since 3.0.0
 *
 * @copyright Copyright (c) 2024 Intewell Inc. All Rights Reserved.
 *
 */

#include "futex.h"
#include "list.h"
#include "syscall_internal.h"
#include <assert.h>
#include <period_sched_group.h>
#include <process_signal.h>
#include <sys/wait.h>
#include <tasklist_lock.h>
#include <time/posix_timer.h>
#include <ttos.h>
#include <ttosInterTask.inl>
#include <ttosProcess.h>
#include <uaccess.h>

#undef KLOG_TAG
#define KLOG_TAG "Exit"
#include <klog.h>

/**
 * @brief 系统调用实现：终止进程。
 *
 * 该函数实现了一个系统调用，用于终止当前进程。
 * 进程的所有资源将被释放，状态码返回给父进程。
 *
 * @param[in] error_code 退出状态码：
 *                 - 0: 正常退出
 *                 - 非0: 错误退出
 * @return 从不返回。
 *
 * @note 1. 该调用永远不会返回到调用进程。
 *       2. 退出状态可通过wait/waitpid获取。
 *       3. 进程的所有资源都会被释放。
 *       4. 子进程会被init进程接管。
 */
DEFINE_SYSCALL(exit, (int error_code))
{
    pcb_t pcb = ttosProcessSelf();
    assert(pcb != NULL);

    if (pcb == pcb->group_leader && list_count(&pcb->thread_group) > 1)
    {
        /* 如果是线程组的组长，且线程组不为空，则退出线程组 */
        process_exit_group(error_code, true);
    }

    if (pcb->clear_child_tid)
    {
        uint32_t __user *clear_child_tid = (uint32_t __user *)pcb->clear_child_tid;

        pcb->clear_child_tid = 0;
        int value = 0;

        copy_to_user(clear_child_tid, &value, sizeof(int));
        SYSCALL_FUNC(futex)(clear_child_tid, FUTEX_WAKE, 1, NULL, NULL, 0);
    }

    pcb->exit_code = error_code;

    process_exit(pcb);

    return 0;
}

/**
 * @brief 进程退出函数。
 *
 * 该函数负责进程的退出流程，包括资源释放、状态更新等。
 *
 * @param pcb 进程控制块。
 */
void process_exit(pcb_t pcb)
{
    KLOG_D("process_exit called pcb %p(%d)", pcb, pcb->taskControlId->tid);

    if (pcb->vfork_done)
    {
        vfork_exit_wake(pcb);
    }

    process_destroy(pcb);

    KLOG_D("process_exit finish");
}

/**
 * @brief ptrace通知函数。
 *
 * 该函数负责在进程退出时通知父进程，并唤醒父进程。
 *
 * @param exit_code 退出状态码。
 */
void ptrace_notify(int exit_code)
{
    pcb_t pcb = ttosProcessSelf();
    pcb_t parent_process = pcb->group_leader->parent;

    if (pcb && (pcb->group_leader->ptrace & PT_PTRACED))
    {
        /* Let the debugger run.  */
        if (pcb->group_leader->parent)
        {
            pcb->exit_signal = exit_code;
            pcb->exit_state = EXIT_TRACE;

            pid_t pid = get_process_pid(pcb->group_leader->parent);
            kernel_signal_kill(pid, TO_PROCESS, SIGCHLD, SI_KERNEL, NULL);
        }

        assert(parent_process != NULL);

        TTOS_SignalSuspendTask(pcb->taskControlId);
    }
}

/**
 * @brief 进程自动收割函数。
 *
 * 该函数决定子进程是否需要进入僵尸态。
 *
 * @param pcb 进程控制块。
 * @return true: 自动收割，false: 不自动收割。
 */
static bool process_autoreap(pcb_t pcb)
{
    /* 决定子进程是否需要进入僵尸态 */
    struct sighand_struct *psig;
    long hand_irq_flag;
    bool autoreap = false;

    /* 如果pcb->group_leader->ptrace条件成立，交由 do_notify_parent 处理 */
    if (pcb->parent == NULL)
    {
        if (pcb->group_leader->ptrace)
        {
            /* 进程不主动释放资源，而是在外层设置为僵尸态并通知其父进程 */
            return false;
        }

        return true;
    }

    psig = get_process_sighand(pcb->parent);

    spin_lock_irqsave(&psig->siglock, hand_irq_flag);
    if (psig->sig_action[SIGCHLD - 1].__sa_handler.sa_handler == PROCESS_SIG_ACT_IGN ||
        (psig->sig_action[SIGCHLD - 1].sa_flags & SA_NOCLDWAIT))
    {
        /**
         * 如果父进程对SIGCHLD设置了IGnore的处理行为，或者设置了SA_NOCLDWAIT 标志位，
         * 则自动收集僵尸态的子进程，不留给waitpid处理，进而无需成为僵尸进程。
         * 但是同样需要唤醒在等待的父进程。
         */
        autoreap = true;
    }

    spin_unlock_irqrestore(&psig->siglock, hand_irq_flag);

    return autoreap;
}

/**
 * @brief 通知父进程函数。
 *
 * 该函数负责通知父进程，并唤醒父进程。
 *
 * @param pcb 进程控制块。
 */
static void do_notify_parent(pcb_t pcb)
{
    pid_t pid;

    if (pcb->parent == NULL)
    {
        if (pcb->group_leader->ptrace)
        {
            process_wakeup_waiter(pcb);
            pid = get_process_pid(pcb->group_leader->parent);
            KLOG_D("ptrace notify parent %p(%d) and send SIGCHLD", pcb->group_leader->parent, pid);
            kernel_signal_kill(pid, TO_PROCESS, SIGCHLD, SI_USER, 0);
        }
        return;
    }

    process_wakeup_waiter(pcb);
    pid = get_process_pid(pcb->parent);
    kernel_signal_kill(pid, TO_PROCESS, SIGCHLD, SI_USER, 0);
}

/**
 * @brief 托孤函数。
 *
 * 该函数负责为当前进程的子进程寻找新的父进程。
 *
 * @param pcb 进程控制块。
 */
static void child_reparent(pcb_t pcb)
{
    if (pcb->first_child != NULL)
    {
        pcb_t init_pcb = pcb_get_by_pid(1);
        if (init_pcb != NULL)
        {
            pcb_t child = pcb->first_child;
            while (child != NULL)
            {
                child->parent = init_pcb;
                child = child->sibling;
            }

            // 将当前的子进程链表连接到 init_pcb 的子进程链表末尾
            if (init_pcb->first_child == NULL)
            {
                init_pcb->first_child = pcb->first_child;
            }
            else
            {
                pcb_t last_child = init_pcb->first_child;
                while (last_child->sibling != NULL)
                {
                    last_child = last_child->sibling;
                }
                last_child->sibling = pcb->first_child;
            }
        }
        else
        {
            pcb_t child = pcb->first_child;
            while (child != NULL)
            {
                child->parent = NULL;
                child = child->sibling;
            }
        }
    }
}

/**
 * @brief 进程销毁函数。
 *
 * 该函数负责进程的销毁流程，包括资源释放、状态更新等。
 *
 * @param pcb 进程控制块。
 */
void process_destroy(pcb_t pcb)
{
    bool autoreap = true;
    struct process_obj *obj, *n;
    TASK_ID task = pcb->taskControlId;
    T_TTOS_ReturnCode ret = TTOS_OK;

    /* 等待PCB对象可被释放，避免在删除任务时，任务还在使用PCB对象 */
    pcb_wait_to_free(pcb);

    arch_cpu_int_disable();

    tasklist_lock();

    clean_process_posix_timer(pcb);

    /* 避免进程退出和进程组退出导致重复清理 */
    if (pcb->exit_state == EXIT_ZOMBIE)
    {
        arch_cpu_int_enable();
        tasklist_unlock();
        return;
    }

    KLOG_D("process destroy, tid %d, pcb %p %s", pcb->taskControlId->tid, pcb, pcb->cmd_name);

    period_sched_group_exit(pcb);

    TRACING_OBJ_CALL(process, destroy, pcb);

    /* 托孤 */
    child_reparent(pcb);

    /* 删除除了pid以外的所有进程对象 */
    list_for_each_entry_safe(obj, n, &pcb->obj_list, list)
    {
        if (obj == pcb->pid)
        {
            continue;
        }
        process_obj_destroy(obj);
    }

    /* 退出时清理进程的ptrace状态，waitpid中将其当作僵尸进程处理 */
    if (pcb->ptrace)
        pcb->ptrace = 0;

    /* 根据状态决定是否自动执行收割，不进入僵尸态 */
    autoreap = process_autoreap(pcb);
    if (autoreap)
    {
        /* 进程不进入僵尸态，稍后直接释放所有资源 */
        pcb->exit_state = EXIT_DEAD;
        KLOG_D("pcb %p(%d) is DEAD", pcb, pcb->taskControlId->tid);
    }
    else
    {
        /* 设置为僵尸态，等待waitpid调用并收割 */
        pcb->exit_state = EXIT_ZOMBIE;
        KLOG_D("pcb %p(%d) is ZOMBIE", pcb, pcb->taskControlId->tid);
    }

    /* 退出进程并释放所有资源 */
    if (pcb->exit_state == EXIT_DEAD)
    {
        process_release_zombie(pcb);
        arch_cpu_int_enable();
        /* 解锁 */
        tasklist_unlock();
        goto delete_exit;
    }
    else
        /* 通知并唤醒父进程，由父进程释放自身的其他资源 */
        do_notify_parent(pcb);

    arch_cpu_int_enable();

    /* 解锁 */
    tasklist_unlock();

    /* 对于线程，在此处认为已经退出，保留pcb信息，设置标志位并挂起，等待回收僵尸态时唤醒 */
    if (task->tid >= CONFIG_PROCESS_MAX)
    {
        ret = task_delete_suspend(task);
        if (ret != TTOS_OK)
        {
            printk("%s %d, thread exit and suspend failed, ret = %d\n", __func__, __LINE__, ret);
            assert(0);
        }
    }

delete_exit:
    /* task与pcb分离 */
    task->ppcb = NULL;

    KLOG_D("task delete called");
    TTOS_DeleteTask(task);
    assert(0);
}
