/**
 * @file    kernel/task/ttosEnablePreempt.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * ac006b61 2024-07-02 移除一级ttos目录
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 9fa804c4 2024-03-22 同一编码格式为UTF-8
 * c7bbbfca 2024-03-18 提交任务功能模块
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/



/* @<MODULE */

/************************头 文 件******************************/

/* @<MOD_HEAD */
#include <ttosBase.h>
/* @MOD_HEAD> */

/************************宏 定 义******************************/
/************************类型定义******************************/
/************************外部声明******************************/
/* @<MOD_EXTERN */

/* @MOD_EXTERN> */

/************************前向声明******************************/
/************************模块变量******************************/
/************************全局变量******************************/
/************************实   现*******************************/

/* @MODULE> */

/**
 * @brief:
 *    使能任务的可抢占。
 * @return:
 *    无
 * @notes:
 *    当使能抢占之后，其他高优先级任务可以抢占当前任务的CPU使用权限。
 * @implements: RTE_DTASK.43.1
 */
void TTOS_EnablePreempt (void)
{
    T_TTOS_TaskControlBlock *task = ttosGetRunningTask ();

    /* @REPLACE_BRACKET: TRUE == ttosIsISR()  */
    if (TRUE == ttosIsISR ())
    {
        return;
    }
    if (task == NULL)
    {
        return;
    }

    /* @REPLACE_BRACKET: U(0) != task->preempted  */
    if (0U != task->preempted)
    {
        /* @REPLACE_BRACKET: (--task->preempted) == U(0)  */
        task->preempted--;
        if ((task->preempted) == 0U)
        {
            /*进行任务调度 */
            (void)ttosSchedule ();
        }
    }
}
