/**
 * @file    kernel/task/ttosDisablePreempt.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * ac006b61 2024-07-02 移除一级ttos目录
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 9fa804c4 2024-03-22 同一编码格式为UTF-8
 * c7bbbfca 2024-03-18 提交任务功能模块
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/


/* @<MODULE */

/************************头 文 件******************************/

/* @<MOD_HEAD */
#include <ttosBase.h>
/* @MOD_HEAD> */

/************************宏 定 义******************************/
/************************类型定义******************************/
/************************外部声明******************************/
/* @<MOD_EXTERN */

/* @MOD_EXTERN> */

/************************前向声明******************************/
/************************模块变量******************************/
/************************全局变量******************************/
/************************实   现*******************************/

/* @MODULE> */

/**
 * @brief:
 *    禁止任务的可抢占。
 * @return:
 *    无
 * @notes:
 *    当禁止抢占之后，只要当前任务的状态为运行态，即使有更高优先级任务就绪也不进行任务切换。
 * @implements: RTE_DTASK.7.1
 */
void TTOS_DisablePreempt (void)
{
    /* @REPLACE_BRACKET: TRUE == ttosIsISR()  */
    if (TRUE == ttosIsISR ())
    {
        return;
    }

    ttosGetRunningTask ()->preempted++;
}
