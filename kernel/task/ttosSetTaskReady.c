/**
 * @file    kernel/task/ttosSetTaskReady.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 * 
 * 0b4f9186 2024-07-17 合并科银周期任务bug解决方案到此
 * ac006b61 2024-07-02 移除一级ttos目录
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 27ecdd3c 2024-04-24 移除旧的posix接口相关代码
 * 33a84634 2024-04-11 添加musl库 移除标准库头文件屏蔽原posix相关头问题
 * 9fa804c4 2024-03-22 同一编码格式为UTF-8
 * c7bbbfca 2024-03-18 提交任务功能模块
 * 
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
*/



/* @<MODULE */

/************************头 文 件******************************/

/* @<MOD_HEAD */
#include <ttosBase.h>
#include <ttosInterHal.h>
#include <ttosInterTask.inl>
#include <ttosUtils.inl>
/* @MOD_HEAD> */

/************************宏 定 义******************************/
/************************类型定义******************************/
/************************外部声明******************************/
T_EXTERN T_VOID TTOS_TaskToReadyHook(TASK_ID Task);
/************************前向声明******************************/
/************************模块变量******************************/
/************************全局变量******************************/
/************************实   现*******************************/

/* @MODULE> */

/*
 * @brief:
 *    设置任务为就绪任务。
 * @param[out]||[in]: task: 任务控制块
 * @return:
 *    无
 * @implements: RTE_DTASK.33.1
 */
void ttosSetTaskReady (T_TTOS_TaskControlBlock *task)
{
    if (task->state & (TTOS_TASK_READY | TTOS_TASK_SUSPEND))
    {
        return;
    }

    /* @KEEP_COMMENT: 将task任务的状态设置为当前状态与TTOS_TASK_READY的复合态 */
    task->state |= (T_UWORD)TTOS_TASK_READY;

    /* @KEEP_COMMENT: 调用TTOS_TaskToReadyHook() */
    TTOS_TaskToReadyHook(task);

    /*
     *绑定的运行任务变为非绑定的运行任务时，或者重启非绑定的运行任务时，不能插入
     *就绪队列，否则此任务可能同时在多个CPU上运行，系统的运行是不可预期的。
     *非绑定的运行任务的条件是((CPU_NONE == task->smpInfo.affinityCpuIndex
     *)&&(CPU_NONE != task->smpInfo.cpuIndex))
     */
#if defined(CONFIG_SMP)

    /* @REPLACE_BRACKET: 非绑定的运行任务是从就绪队列摘除了的*/
    if ((CPU_NONE != task->smpInfo.affinityCpuIndex)
        || (CPU_NONE == task->smpInfo.cpuIndex))
    {
#endif
        /*
         * @KEEP_COMMENT:
         * 设置<task>任务的优先级在优先级位图ttosPriorityBitMap中的相应位
         * 设置优先级位图，并将Task加入到其优先级所在的就绪队列尾部
         */
        TBSP_SET_PRIORITYBITMAP (task->smpInfo.affinityCpuIndex,
                                 task->taskCurPriority);
        /* @KEEP_COMMENT:
         * 将<task>任务插入对应优先级就绪队列ttosManager.priorityQueue尾部 */
        ttosReadyQueuePut (task, FALSE);
        /* @KEEP_COMMENT: 复位<task>的时间片大小 */
        task->tickSlice = task->tickSliceSize;
        /* @KEEP_COMMENT:
         * 重新获取最高优先级任务，保存于ttosManager.priorityQueueTask */
        ttosSetHighestPriorityTask (task->smpInfo.affinityCpuIndex);
#if defined(CONFIG_SMP)
    }
#endif
    /* 任务从非就绪变为就绪,检查是否需要对非当前CPU发送重调度IPI */
    (void)ttosTaskStateChanged (task, TRUE);
}
