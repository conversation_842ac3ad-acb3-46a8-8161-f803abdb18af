/**
 * @file    kernel/process/procfs/pid_cmdline.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 0c106b3f 2024-07-24 修改规范INIT API
 * ac006b61 2024-07-02 移除一级ttos目录
 * db323acd 2024-06-22 增加cmdline 修复comm大小
 * a2a61100 2024-06-22 添加/proc/pid/comm
 * 6ab36aa4 2024-06-21 更新初始化信息 添加 /proc/uptime
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

/* /proc/[pid]/cmdline */
#include <fs/procfs.h>
#include <stdio.h>
#include <ttosProcess.h>

struct seq_data
{
    char buf[128];
    struct proc_seq_buf seq;
};

static int proc_file_open(struct proc_dir_entry *inode, void *buffer)
{
    struct seq_data *data = calloc(1, sizeof(struct seq_data));
    if (!data)
    {
        return -ENOMEM;
    }
    *(struct seq_data **)buffer = data;
    return 0;
}

static ssize_t proc_file_read(struct proc_dir_entry *inode, void *priv, char *buffer, size_t count,
                              off_t *ppos)
{
    struct seq_data *data = (struct seq_data *)priv;

    TASK_ID task;
    pcb_t pcb = pcb_get_by_pid_nt(inode->pid);
    if (pcb == NULL)
    {
        // task
        return 0;
        task = task_get_by_tid(inode->pid);
        if (task == NULL)
        {
            return -ENOENT;
        }
        pcb = task->ppcb;
    }

    if (pcb == NULL)
    {
        // kernel task no cmdline
        return 0;
    }
    
    int ret = 0;

    if (*ppos == 0)
    {
        /* 初始化序列化缓冲区 */
        proc_seq_init(&data->seq, data->buf, sizeof(data->buf), ppos);
        if(pcb_get(pcb))
        {
            proc_seq_printf(&data->seq, "%s\n", (char *)pcb->cmdline->really->obj);
            pcb_put(pcb);
        }
    }
    ret = proc_seq_read(&data->seq, buffer, count);
    return ret;
}

static int proc_file_release(struct proc_dir_entry *inode, void *buffer)
{
    struct seq_data *data = buffer;
    if (data)
    {
        free(data);
    }
    return 0;
}

static struct proc_ops ops = {
    .proc_open = proc_file_open, .proc_read = proc_file_read, .proc_release = proc_file_release};

int create_pid_cmdline(pid_t pid, struct proc_dir_entry *parent)
{
    struct proc_dir_entry *entry;
    entry = proc_create("cmdline", 0444, parent, &ops);
    entry->pid = pid;
    return 0;
}