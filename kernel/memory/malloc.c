/**
 * @file    kernel/memory/malloc.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 0c106b3f 2024-07-24 修改规范INIT API
 * 121c12ee 2024-07-22 1. elf文件添加shdr信息 2. coreudump添加用户程序的text,
 * data, bss段信息 3.删除多余的代码，并通过链表管理core dump中的section内存
 * 3d167baa 2024-07-10 coredump代码框架完成
 * 49945c49 2024-07-10 添加dma内存分配接口
 * ac006b61 2024-07-02 移除一级ttos目录
 * ab6f7c2c 2024-06-19 修复ubsan错误
 * f5288453 2024-06-17 添加kasan
 * 7c3b731f 2024-05-17 格式化代码
 * b5423b46 2024-05-17 增加procfs meminfo
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 372c7f06 2024-05-09 修复 calloc错误分配内存的问题
 * 5a2d53e6 2024-05-05 增加libk同时移除对libc的依赖
 * 6164e9c1 2024-04-24 添加内核堆信息打印命令 free
 * d6c43fc4 2024-04-19 添加进程
 * 52902aeb 2024-04-19 移除workspace概念 添加tlsf和原来的内存分配算法可选
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include "kasan/kasan.h"
#include "page.h"
#include <assert.h>
#include <kmalloc.h>
#include <malloc.h>
#include <spinlock.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <symtab.h>
#include <system/gfp_types.h>
#include <ttosMM.h>
#include <ttos_init.h>

#ifdef CONFIG_COREDUMP
#include <coredump.h>
#endif

#ifdef CONFIG_WORKSPACE
#include "workspace/ttosHeap.h"
#include <ttosBase.h>
#include <ttosTypes.h>

/* 工作空间控制变量 */
T_TTOS_HeapControl g_kernel_hcb;
T_TTOS_HeapControl g_dma_hcb;
T_TTOS_HeapControl g_kernelmodule_hcb;

typedef T_TTOS_HeapControl *heap_t;
#elif defined(CONFIG_TLSF)
#include "tlsf/tlsf.h"
typedef tlsf_t heap_t;

#elif defined(CONFIG_SLUB)
#include <slub.h>
#endif /* CONFIG_SLUB */

static void *__kernel_malloc_impl(size_t size);
static void *__dma_malloc_impl(size_t size);
static void *__dma_aligned_alloc_impl(size_t align, size_t len);
static void __kernel_free_impl(void *p);
static void *__kernel_aligned_alloc_impl(size_t align, size_t len);
static void *__kernel_calloc_impl(size_t m, size_t n);
static void *__kernel_realloc_impl(void *p, size_t n);
static void *__kernelmodule_malloc_impl(size_t size);
static void *__kernel_zalloc_impl(size_t n);

KSYM_EXPORT_ALIAS(__kernel_malloc_impl, malloc);
KSYM_EXPORT_ALIAS(__dma_malloc_impl, dma_malloc);
KSYM_EXPORT_ALIAS(__dma_aligned_alloc_impl, dma_memalign);
KSYM_EXPORT_ALIAS(__kernel_free_impl, free);
KSYM_EXPORT_ALIAS(__kernel_aligned_alloc_impl, memalign);
KSYM_EXPORT_ALIAS(__kernel_calloc_impl, calloc);
KSYM_EXPORT_ALIAS(__kernel_realloc_impl, realloc);
KSYM_EXPORT_ALIAS(__kernel_zalloc_impl, zalloc);
#ifdef CONFIG_MODULES
__weak_alias(__kernelmodule_malloc_impl, kernelmodule_malloc);
#endif

#ifndef CONFIG_SLUB

#define MAX_HEAP 3

#define KERNEL_HEAP_INDEX 0
#define DMA_HEAP_INDEX 1
#define KERNELMODULE_HEAP_INDEX 2

struct heap_node
{
    uintptr_t start;
    uintptr_t end;
    gfp_t type;
    heap_t handler;
    struct mm_region region;
} g_heap_table[MAX_HEAP];

#define kernel_heap ((heap_t)g_heap_table[KERNEL_HEAP_INDEX].handler)
#define dma_heap ((heap_t)g_heap_table[DMA_HEAP_INDEX].handler)
#define kernelmodule_heap ((heap_t)g_heap_table[KERNELMODULE_HEAP_INDEX].handler)

static DEFINE_SPINLOCK(g_malloc_lock);

static heap_t ptr2heap(void *ptr)
{
    int i;
    for (i = 0; i < MAX_HEAP; i++)
    {
        if ((uintptr_t)ptr < g_heap_table[i].end && (uintptr_t)ptr > g_heap_table[i].start)
        {
            return g_heap_table[i].handler;
        }
    }
    assert(0 && "ptr can not free");
    return NULL;
}

size_t __kernel_heap_block_size(void *p)
{
#ifdef CONFIG_WORKSPACE
    size_t size;
    ttosGetBlockSizeHeap(kernel_heap, p, &size);
    return size;
#elif defined(CONFIG_TLSF)
    return tlsf_block_size(p);
#endif
}

static void *__kernel_malloc_impl(size_t size)
{
    void *mem;
    uintptr_t flags;
    spin_lock_irqsave(&g_malloc_lock, flags);
#ifdef CONFIG_WORKSPACE
    ttosAllocateHeap(kernel_heap, size, 8, &mem);
#elif defined(CONFIG_TLSF)
    mem = tlsf_memalign(kernel_heap, 8, size);
#endif
    spin_unlock_irqrestore(&g_malloc_lock, flags);

    if (mem)
    {
        kasan_unpoison(mem, __kernel_heap_block_size(mem));
    }

    return mem;
}

static void *__dma_malloc_impl(size_t size)
{
    void *mem;
    uintptr_t flags;
    spin_lock_irqsave(&g_malloc_lock, flags);
#ifdef CONFIG_WORKSPACE
    ttosAllocateHeap(dma_heap, size, 8, &mem);
#elif defined(CONFIG_TLSF)
    mem = tlsf_memalign(dma_heap, 8, size);
#endif
    spin_unlock_irqrestore(&g_malloc_lock, flags);

    if (mem)
    {
        kasan_unpoison(mem, __kernel_heap_block_size(mem));
    }

    return mem;
}

static void *__dma_aligned_alloc_impl(size_t align, size_t len)
{
    void *mem;
    uintptr_t flags;
    spin_lock_irqsave(&g_malloc_lock, flags);
#ifdef CONFIG_WORKSPACE
    ttosAllocateHeap(dma_heap, len, align, &mem);
#elif defined(CONFIG_TLSF)
    mem = tlsf_memalign(dma_heap, align, len);
#endif
    spin_unlock_irqrestore(&g_malloc_lock, flags);
    if (mem)
    {
        kasan_unpoison(mem, __kernel_heap_block_size(mem));
    }
    return mem;
}

static void __kernel_free_impl(void *p)
{
    uintptr_t flags;

    if (p)
    {
        kasan_poison(p, __kernel_heap_block_size(p));
    }

    spin_lock_irqsave(&g_malloc_lock, flags);
#ifdef CONFIG_WORKSPACE
    ttosFreeHeap(ptr2heap(p), p);
#elif defined(CONFIG_TLSF)
    tlsf_free(ptr2heap(p), p);
#endif
    spin_unlock_irqrestore(&g_malloc_lock, flags);
}

static void *__kernel_aligned_alloc_impl(size_t align, size_t len)
{
    void *mem;
    uintptr_t flags;
    spin_lock_irqsave(&g_malloc_lock, flags);
#ifdef CONFIG_WORKSPACE
    ttosAllocateHeap(kernel_heap, len, align, &mem);
#elif defined(CONFIG_TLSF)
    mem = tlsf_memalign(kernel_heap, align, len);
#endif
    spin_unlock_irqrestore(&g_malloc_lock, flags);
    if (mem)
    {
        kasan_unpoison(mem, __kernel_heap_block_size(mem));
    }
    return mem;
}

static void *__kernel_calloc_impl(size_t m, size_t n)
{
    size_t s = n * m;
    void *p = malloc(s);
    if (p == NULL)
    {
        return NULL;
    }
    memset(p, 0, s);
    return p;
}

static void *__kernel_realloc_impl(void *p, size_t n)
{
    void *mem;

    if (p == NULL)
    {
        return __kernel_malloc_impl(n);
    }

#if defined(CONFIG_WORKSPACE) || defined(CONFIG_MM_KASAN)
    size_t old_size;

    old_size = __kernel_heap_block_size(p);

    if (n <= old_size)
    {
        return p;
    }

    mem = __kernel_malloc_impl(n);
    memcpy(mem, p, old_size);
    __kernel_free_impl(p);
#elif defined(CONFIG_TLSF)
    uintptr_t flags;
    spin_lock_irqsave(&g_malloc_lock, flags);
    mem = tlsf_realloc(kernel_heap, p, n);
    spin_unlock_irqrestore(&g_malloc_lock, flags);
#endif

    return mem;
}

static void *__kernel_zalloc_impl(size_t n)
{
    void *p = malloc(n);
    if (p == NULL)
    {
        return NULL;
    }
    memset(p, 0, n);
    return p;
}

#ifdef CONFIG_MODULES
static void *__kernelmodule_malloc_impl(size_t size)
{
    void *mem;
    uintptr_t flags;
    spin_lock_irqsave(&g_malloc_lock, flags);
#ifdef CONFIG_WORKSPACE
    ttosAllocateHeap(kernelmodule_heap, size, 8, &mem);
#elif defined(CONFIG_TLSF)
    mem = tlsf_memalign(kernelmodule_heap, 8, size);
#endif
    spin_unlock_irqrestore(&g_malloc_lock, flags);

    if (mem)
    {
        kasan_unpoison(mem, __kernel_heap_block_size(mem));
    }

    return mem;
}
#endif

extern int __end__;
extern int __executable_start;
static int __kernel_malloc_init(void)
{
    void *heapstart;
    size_t heapsize;

    heapstart = (void *)page_address(pages_alloc(page_bits(CONFIG_KERNEL_HEAP_SIZE), ZONE_NORMAL));
    heapsize = CONFIG_KERNEL_HEAP_SIZE;

    kasan_register(heapstart, &heapsize);

    g_heap_table[KERNEL_HEAP_INDEX].start = (uintptr_t)heapstart;
    g_heap_table[KERNEL_HEAP_INDEX].end = (uintptr_t)heapstart + heapsize;
    g_heap_table[KERNEL_HEAP_INDEX].type = GFP_KERNEL;

#ifdef CONFIG_WORKSPACE
    g_heap_table[KERNEL_HEAP_INDEX].handler = &g_kernel_hcb;
    ttosInitHeap(kernel_heap, heapstart, heapsize);
#elif defined(CONFIG_TLSF)
    g_heap_table[KERNEL_HEAP_INDEX].handler = tlsf_create_with_pool(heapstart, heapsize);
#endif

    heapstart =
        (void *)page_address(pages_alloc(page_bits(CONFIG_KERNEL_NC_HEAP_SIZE), ZONE_NORMAL));
    heapsize = CONFIG_KERNEL_NC_HEAP_SIZE;

    ttosSetPageAttribute((virt_addr_t)heapstart, heapsize, MT_NCACHE | MT_KERNEL);

    kasan_register(heapstart, &heapsize);

#ifdef CONFIG_WORKSPACE
    g_heap_table[DMA_HEAP_INDEX].handler = &g_dma_hcb;
    ttosInitHeap(dma_heap, heapstart, heapsize);
#elif defined(CONFIG_TLSF)
    g_heap_table[DMA_HEAP_INDEX].handler = tlsf_create_with_pool(heapstart, heapsize);
#endif

#ifdef CONFIG_MODULES

    heapstart = (void *)(virt_addr_t)ALIGN_UP((phys_addr_t)(virt_addr_t)&__end__, PAGE_SIZE);
    heapsize = 0x4000000;

    kasan_register(heapstart, &heapsize);

    g_heap_table[KERNELMODULE_HEAP_INDEX].start = (uintptr_t)heapstart;
    g_heap_table[KERNELMODULE_HEAP_INDEX].end = (uintptr_t)heapstart + heapsize;
    g_heap_table[KERNELMODULE_HEAP_INDEX].type = GFP_KERNEL;

#ifdef CONFIG_WORKSPACE
    g_heap_table[KERNELMODULE_HEAP_INDEX].handler = &g_kernelmodule_hcb;
    ttosInitHeap(kernelmodule_heap, heapstart, heapsize);
#elif defined(CONFIG_TLSF)
    g_heap_table[KERNELMODULE_HEAP_INDEX].handler = tlsf_create_with_pool(heapstart, heapsize);
#endif

#endif
    return 0;
}
INIT_EXPORT_PRE(__kernel_malloc_init, "init malloc");

#if defined(CONFIG_TLSF)
static void __tlsf_walker(void *ptr, size_t size, int used, void *user)
{
    struct memory_info *info = (struct memory_info *)user;
    info->total += size;
    if (!used)
    {
        info->free += size;
    }
    else
    {
        info->used += size;
    }
}
#endif /* CONFIG_TLSF */
static struct memory_info meminfo;

static void _get_malloc_info(heap_t heap, struct memory_info *info)
{
#ifdef CONFIG_WORKSPACE
    info->free = heap->heap_size - heap->in_use_size;
    info->total = heap->heap_size;
    info->used = heap->in_use_size;
#elif defined(CONFIG_TLSF)
    info->free = 0;
    info->total = 0;
    info->used = 0;
    tlsf_walk_pool(tlsf_get_pool(heap), __tlsf_walker, info);
#endif
}

struct memory_info *get_malloc_info(void)
{
    _get_malloc_info(kernel_heap, &meminfo);
    return &meminfo;
}

#else

static void *__kernel_malloc_impl(size_t size)
{
    void *mem = kmalloc(size, GFP_KERNEL);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}

static void *__kernel_zalloc_impl(size_t n)
{
    void *mem = kzalloc(n, GFP_KERNEL);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}

static void *__dma_malloc_impl(size_t size)
{
    void *mem = kmalloc(size, GFP_DMA32);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}

static void *__dma_aligned_alloc_impl(size_t align, size_t len)
{
    /* slub 分配的内存天然是对齐的,依据C11标准len必须是 align的整数倍 所以这里必然是对齐的 */
    void *mem = kmalloc(len, GFP_DMA32);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}
static void __kernel_free_impl(void *p)
{
    if (p)
    {
        kasan_poison(p, ksize(p));
    }
    kfree(p);
}
static void *__kernel_aligned_alloc_impl(size_t align, size_t len)
{
    /* slub 分配的内存天然是对齐的,依据C11标准len必须是 align的整数倍 所以这里必然是对齐的 */
    void *mem = kmalloc(len, GFP_KERNEL);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}
static void *__kernel_calloc_impl(size_t m, size_t n)
{
    // todo kmalloc_array
    void *mem = kzalloc(m * n, GFP_KERNEL);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}
static void *__kernel_realloc_impl(void *p, size_t n)
{
    void *mem = krealloc(p, n, GFP_KERNEL);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}
static void *__kernelmodule_malloc_impl(size_t size)
{
    // todo
    void *mem = kmalloc(size, GFP_KERNEL);
    if (mem)
    {
        kasan_unpoison(mem, ksize(mem));
    }

    return mem;
}

#endif

#if CONFIG_SHELL
#include <shell.h>
static void mem_size_dp(char *tag, unsigned long size)
{
    char s[] = {'K', 'M', 'G'};
    int i = 0;
    double ds = size;

    printf("%-5s: %15ld Bytes", tag, size);

    for (i = 0; i < 3; i++)
    {
        ds /= 1024;
        if (ds < 1024)
            break;
    }

    printf(" (%.3lf %cB)\n", ds, s[i]);
}

#ifndef CONFIG_SLUB

static void _free_dump(char *name, heap_t heap)
{
    struct memory_info meminfo;

    _get_malloc_info(heap, &meminfo);

    printf("|---------- %s memory info ----------|\n", name);
    mem_size_dp("Total", meminfo.total);
    mem_size_dp("Used", meminfo.used);
    mem_size_dp("Free", meminfo.free);
    printf("\n");
}

static int _free_cmd(int argc, const char *argv[])
{
    _free_dump("Kernel", kernel_heap);
    _free_dump("kernel Module", kernelmodule_heap);
    _free_dump("DMA", dma_heap);
    return 0;
}
#else
static int _free_cmd(int argc, const char *argv[])
{
    // slub_dump();
    return 0;
}
#endif
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_MAIN) |
                     SHELL_CMD_DISABLE_RETURN,
                 free, _free_cmd, list heap free);
#endif /* CONFIG_SHELL */
