
#ifndef __K_TIME__
#define __K_TIME__

#include <list.h>
#include <process_signal.h>
#include <spinlock.h>
#include <stdbool.h>
#include <sys/time.h>
#include <system/types.h>
#include <time.h>
#include <timer_event/timer.h>

#define __NEED_clock_t
#include <bits/alltypes.h>

#define TTOS_TICK_PER_SEC TTOS_GetSysClkRate()

#define MSEC_PER_SEC (1000ULL)
#define USEC_PER_MSEC (1000ULL)
#define NSEC_PER_USEC (1000ULL)
#define NSEC_PER_MSEC (1000000ULL)
#define USEC_PER_SEC (1000000ULL)
#define NSEC_PER_SEC (1000000000ULL)
#define FSEC_PER_SEC (1000000000000000ULL)

#define MUSL_SC_CLK_TCK 100

struct k_clock;
struct timespec;
struct timespec64;

struct itimerspec64
{
    struct timespec64 it_interval;
    struct timespec64 it_value;
};

struct itimerspec32
{
    struct timespec32 it_interval;
    struct timespec32 it_value;
};

struct k_itimer
{
    struct list_head node;
    ttos_spinlock_t it_lock;
    clockid_t it_clock;
    const struct k_clock *kclock;
    struct timer_event timer_id;
    struct siginfo siginfo;
    struct itimerspec64 timer_val;
    int overcount;
    pcb_t pcb;
    int sigev_notify;
    int glist_id;
};

/* Located here for timespec[64]_valid_strict */
#define TIME64_MAX ((s64) ~((u64)1 << 63))
#define TIME64_MIN (-TIME64_MAX - 1)

#define KTIME_MAX ((s64) ~((u64)1 << 63))
#define KTIME_MIN (-KTIME_MAX - 1)
#define KTIME_SEC_MAX (KTIME_MAX / NSEC_PER_SEC)
#define KTIME_SEC_MIN (KTIME_MIN / NSEC_PER_SEC)

/*
 * Returns true if the timespec64 is norm, false if denorm:
 */
static inline bool timespec64_valid(const struct timespec64 *ts)
{
    /* Dates before 1970 are bogus */
    if (ts->tv_sec < 0)
        return false;
    /* Can't have more nanoseconds then a second */
    if ((unsigned long)ts->tv_nsec >= NSEC_PER_SEC)
        return false;
    return true;
}

/**
 * timespec64_to_ns - Convert timespec64 to nanoseconds
 * @ts:		pointer to the timespec64 variable to be converted
 *
 * Returns the scalar nanosecond representation of the timespec64
 * parameter.
 */
static inline s64 timespec64_to_ns(const struct timespec64 *ts)
{
    /* Prevent multiplication overflow / underflow */
    if (ts->tv_sec >= KTIME_SEC_MAX)
        return KTIME_MAX;

    return ((s64)ts->tv_sec * NSEC_PER_SEC) + ts->tv_nsec;
}

u64 kernel_realtime_current_ns_get(void);

void kernel_realtime64_get(const clockid_t which_clock, struct timespec64 *tp);

int kernel_clock_nanosleep(clockid_t clockid, int flags, struct timespec64 *rqtp,
                           struct timespec64 *rmtp);
int kernel_nanosleep(struct timespec64 *_rqtp, struct timespec64 *_rmtp);
int kernel_clock_settime(clockid_t clock_id, const struct timespec64 *tp);
int kernel_clock_gettime(clockid_t clk, struct timespec64 *ts);
int kernel_gettimeofday(struct timeval *tp, struct timezone *tzp);
int kernel_settimeofday(const struct timeval *tp, const struct timezone *tzp);
int kernel_clock_getres(clockid_t clockid, struct timespec64 *res);
time_t kernel_time(void);

clock_t clock_ms_to_tick(int ms);
clock_t timespec64_to_clock_t(const struct timespec64 *time);
int clock_time_to_tick(const struct timespec *time, bool is_abs_timeout);
struct timespec clock_timespec_subtract(const struct timespec *time1, const struct timespec *time2);
struct timespec64 clock_timespec_subtract64(const struct timespec64 *time1,
                                            const struct timespec64 *time2);
struct timespec clock_timespec_add(const struct timespec *time1, const struct timespec *time2);
struct timespec64 clock_timespec_add64(const struct timespec64 *time1,
                                       const struct timespec64 *time2);

struct timespec64 clock_timespec_max64(const struct timespec64 *ts1, const struct timespec64 *ts2);

int clock_time_abs_to_timespec(int id, struct timespec *result, const struct timespec *abstime);
uint64_t timespec64_to_ms(const struct timespec64 *time);

#endif /* __K_TIME__ */
