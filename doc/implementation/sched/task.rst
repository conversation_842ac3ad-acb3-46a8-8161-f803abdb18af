任务
=======

设计思路
~~~~~~~

任务是执行的基本单位,任务采用优先级抢占式调度,相同优先级的任务采用时间片轮转调度。

本文档没有特殊说明外,任务的优先级是指通过任务管理原生接口创建的任务的优先级,此任务的优先级
为0-255,0表示最高优先级,255表示最低优先级。使用POSIX接口创建的任务使用的优先级也是0-255,
但是0表示最低优先级,255表示最高优先级。在POSIX创建任务接口中,会将此任务的优先级转化为任务管
理内部使用的任务优先级,便于任务管理统一调度运行通过任务管理原生接口创建的任务和通过POSIX接口
创建的任务。

任务管理提供周期任务和非周期任务的管理。

非周期任务中存在一个特殊的任务——IDLE任务,IDLE任务优
先级为255,在系统空闲时投入运行。

周期任务
--------
周期任务是指按周期固定运行一定时间,可以延时启动的任务。周期任务具有周期时间、持续时间和延迟
启动时间。周期时间、持续时间和延迟启动时间的单位为tick。

**延迟启动时间** 是在启动周期任务后或重启周期任务后该周期任务等待其周期到来的时间,在这段时间中,
周期任务处于周期等待态。当周期任务周期到来时周期任务才结束周期等待态,并被启动处于就绪态。 **持续时间** 表示周期任务在本周期内完成任务的时间,周期任务被启动处于就绪态后其持续时间开始计时,周
期任务持续时间的结束点被称作周期任务的截止期,周期任务的持续时间必须小于等于周期时间。周期任
务需要在持续时间结束点前执行周期等待接口使周期任务处于完成等待态,等待截止期的到来。周期任务
周期时间定义如图所示。

.. image:: ../../_static/images/task_1.png
   :alt: 周期任务周期时间定义
   :align: center

在每一个周期内,周期任务必须在截止期内完成本周期要完成的任务,否则会出现截止期超时的情况。

当启动周期任务或重启周期任务后,周期任务的周期时间开始计时;当周期任务的周期到来并处于就绪态
后,其持续时间开始计时,此时任务调度会选择最高优先级的第一个就绪任务投入运行。

当有3个运行顺序为周期任务1、周期任务2、周期任务3的周期任务,在 Time0 时,3个就绪的周期任务的周
期均开始,那么周期任务1、周期任务2、周期任务3按顺序投入运行。当周期任务3运行完毕后,如果下一
个周期还未到来,那么操作系统空闲。运行情况如图所示。

.. image:: ../../_static/images/task_2.png
   :alt: 周期任务运行情况
   :align: center

周期任务1的截止期不能晚于 Time1 的时间,周期任务2的截止期不能晚于于 Time2 的时间,周期任务3的截止
期不能晚于 Time3 的时间,否则会出现截止期超时的情况,并且3个周期任务的截止期均不能超过下一个周期
开始的时间, Time0 - Time1 表示周期任务1需要完成任务的时间, Time1 - Time2 表示周期任务2需要完成任务的
时间, Time2 - Time3 表示周期任务3需要完成任务的时间。

任务管理的核心是对任务状态和调度的管理。

任务状态
--------

**任务应具有以下几种状态** :

    1. 休眠: 表示任务已经分配好各种资源,但是还没有被启动;

    #. 就绪: 表示任务已经被初始化,允许参与CPU的竞争;

    #. 运行: 表示任务已经获得了CPU,正在运行中;

    #. 挂起: 表示任务被禁止参加CPU的竞争;

    #. 等待: 表示任务在等待相关资源;
    
    #. 挂等: 表示处于等待并且被挂起,为挂起和等待的复合态;

    #. 周期等待: 表示周期任务在等待周期到来;

    #. 完成等待: 表示周期任务在本周期内执行完成,等待截止期的到来;

    #. 挂起和周期等待复合态:表示周期任务在等待周期到来时并被挂起,为挂起和周期等待的复合态;
    
    #. 挂起和完成等待复合态:表示周期任务在本周期内执行完成,等待截止期到来时并被挂起,为挂起和完成等待的复合态。

非周期任务具有休眠、就绪、运行、挂起、等待、挂等6种状态,状态变迁如图所示。

.. image:: ../../_static/images/task_3.png
   :alt: 非周期任务状变迁
   :align: center

非周期任务状态变迁说明:
----------------------

s: 初始化不需要自动启动的任务后,任务处于休眠态;

a: 表示任务从休眠态变迁为就绪态。如下任一情况可使任务从休眠态变迁为就绪态:

    1) 初始化需要自动启动的任务;
    
    #) 调用启动任务接口启动任务。

b: 表示就绪任务获得CPU投入运行,使任务从就绪态变迁为运行态。

c: 表示任务从就绪态变迁为休眠态。如下情况可使任务从就绪态变迁为休眠态:
    
    * 调用停止任务接口停止就绪态任务。

d: 表示任务从就绪态变迁为挂起态。如下情况可使任务从就绪态变迁为挂起态:
    
    * 调用挂起任务接口挂起就绪态任务。

e: 表示任务从运行态变迁为休眠态。如下情况可使任务从运行态变迁为休眠态:
    
    * 调用停止任务接口停止当前任务。

f: 表示任务从运行态变迁为就绪态。如下任一情况可使任务从运行态变迁为就绪态:

    1) 调用重启任务接口重启当前任务;

    #) 同优先级任务进行时间片轮转时;

    #) 任务主动把CPU让给同等优先级的任务运行时;

    #) 高优先级任务抢占低优先级任务时。

g: 表示任务从运行态变迁为挂起态。如下情况可使任务从运行态变迁为挂起态:
    
    * 调用挂起任务接口挂起当前任务。

h: 表示任务从运行态变迁为等待态。如下任一情况可使任务从运行态变迁为等待态:
    
    1) 调用任务休眠接口使任务等待一段时间;
    
    #) 调用资源等待接口使任务等待资源。

i: 表示任务从挂起态变迁为休眠态。如下情况可使任务从挂起态变迁为休眠态:

    * 调用停止任务接口停止挂起态的任务。

j: 表示任务从挂起态变迁为就绪态。如下任一情况可使任务从挂起态变迁为就绪态:
    
    1) 调用解挂任务接口解挂被挂起的任务;
    
    #) 调用重启任务接口重启被挂起的任务。

k: 表示任务从等待态变迁为挂等态。如下情况可使任务从等待态变迁为挂等态:

    * 调用挂起任务接口挂起处于等待态的任务。

l: 表示任务从等待态变迁为就绪态。如下任一情况可使任务从等待态变迁为就绪态:

    1) 等待的时间到来;

    #) 等待的资源到来;

    #) 调用重启任务接口重启处于等待态的任务。

m: 表示任务从等待态变迁为休眠态。如下情况可使任务从等待态变迁为休眠态:

    * 调用停止任务接口停止处于等待态的任务。

n: 表示任务从挂等态变迁为挂起态。如下任一情况可使任务从挂等态变迁为挂起态:

    1) 任务等待时间到来;

    #) 等待的资源到来。

o: 表示任务从挂等态变迁为等待态。如下情况可使任务从挂等态变迁为等待态:

    * 调用解挂任务接口解挂处于挂等态的任务。

p: 表示任务从挂等态变迁为休眠态。如下情况可使任务从挂等态变迁为休眠态:

    * 调用停止任务接口停止处于挂等态的任务。

q: 表示任务从挂等态变迁为就绪态。如下情况可使任务从挂等态变迁为就绪态:

    * 调用重启任务接口重启处于挂等态的任务。

r: 表示任务从就绪态变迁为就绪态。如下可使任务从就绪态变迁为就绪态:

    * 调用重启任务接口重启处于就绪态的任务。

周期任务的状态变迁
-----------------

周期任务除具有非周期任务的6种状态外,还具有周期等待态、完成等待态、挂起和周期等待复
合态、挂起和完成等待复合态。对于周期任务的休眠、就绪、运行、挂起、等待、挂等6种状态
的变迁同非周期任务,就不再进行描述,仅针对周期任务的周期等待态、完成等待态、挂起和周
期等待复合态、挂起和完成等待复合态的变迁进行描述:

1. 启动周期任务时,如果周期任务不需要延迟启动,那么从休眠态变迁为就绪态;否则从休眠态变
   迁为周期等待态,当周期到来时,从周期等待态变迁为就绪态;

#. 调用周期等待接口通知本周期内任务执行完毕时,周期任务从运行态变迁为完成等待态;

#. 当周期任务的截止期到来时,如果周期任务等待其下一个周期到来的时间不为0,那么从完成等待
   态变迁为周期等待态;否则从完成等待态变迁为就绪态;

#. 调用挂起任务接口挂起处于周期等待态或完成等待态的周期任务时,可使周期任务变迁到挂起和
   周期等待复合态或挂起和完成等待复合态;

#. 调用重启任务接口重启处于周期等待态或完成等待态或挂起和周期等待复合态或挂起和完成等待
   复合态的周期任务时,如果周期任务需要延迟启动,那么周期任务变迁到周期等待态;否则变迁到就绪态;

#. 调用停止任务接口停止处于周期等待态或完成等待态或挂起和周期等待复合态或挂起和完成等待
   复合态的周期任务时,周期任务将变迁到休眠态;

#. 调用解挂任务接口解挂处于挂起和周期等待复合态或挂起和完成等待复合态的周期任务时,周期任
   务将变迁到周期等待态或完成等待态。

IDLE任务只有就绪和运行两种状态,实时内核初始化时创建并启动IDLE任务,IDLE任务变为就绪态,
IDLE任务两种状态的变迁如图所示。

.. image:: ../../_static/images/task_4.png
   :alt: IDLE任务状态变迁
   :align: center

IDLE任务状态变迁说明:
--------------------

a: 当非IDLE任务都放弃CPU时,IDLE任务获得CPU,进入运行态;

b: 当非IDLE任务抢占CPU时,IDLE任务放弃CPU,进入就绪态。

任务调度
--------

任务在运行过程中应可分为不可抢占和可抢占。

1. 不可抢占
    即一旦某个高优先级的任务占有了处理器,就一直运行下去,直到任务由于自身的原因自愿放
    弃处理器时(如任务等待事件),才按优先级进行调度,让另一高优先级任务运行。任务在运行
    过程中可以被中断,中断处理程序在运行过程中即使唤醒了一个更高优先级的任务,在中断处
    理程序完成后还是返回到被中断的任务,只有这个任务放弃了处理器,更高优先级的任务才能
    运行。

#. 可抢占
    任何时刻都严格按照当前优先级最高的任务在处理器上运行的原则进行任务调度,或者说,在
    处理器上运行的任务永远是就绪任务中优先级最高的任务。当优先级高的任务能运行时,保证
    其CPU的时间,让其尽快运行完。如果优先级高的任务因故(如等待事件)暂停运行,从就绪态
    变迁为阻塞态,那么就再次将当前优先级最高的任务投入运行,一旦优先级更高的任务又就绪
    (因事件的到来而成为就绪),任务调度器就迫使当前运行任务马上让出处理器给优先级最高的
    这个任务使用(或称被抢占了处理器)。

    当任务允许抢占时,同一优先级的多个就绪任务可以采用时间片轮转的方式投入运行,即:当
    有两个或多个就绪任务具有相同的优先级且它们是就绪任务中优先级最高的任务时,系统选择
    这组任务中的第一个就绪任务,让它仅运行时间片指定的时间,在运行完一个时间片后,该任
    务即使还没有完成,它也必须释放处理器让下一个与它相同优先级的就绪任务运行(假设这时没
    有更高优先级的任务就绪),而放弃处理器的任务就排到同级优先级最后任务的后面,等待再次
    运行。如果这个任务不支持时间片轮转调度,则只有更高优先级的任务才能抢占此任务的运行。

任务管理使用的队列
-----------------

任务优先级就绪队列
^^^^^^^^^^^^^^^^^

任务管理模块使用任务优先级就绪队列管理操作系统中处于就绪态的任务。

任务优先级共有256级,任务管理为每一级优先级分配一个任务优先级就绪队列,在同一任务优先级
就绪队列中的任务可采用时间片轮转调度。任务管理在选择最高优先级就绪任务时,首先获取有就绪
任务的最高优先级就绪队列,然后再从该任务优先级就绪队列中选取第一个任务投入运行。

任务优先级就绪队列采用双向链表存储结构。例如,在某一任务优先级就绪队列上有5个任务处于就
绪状态,那么这一任务优先级就绪队列如图所示:

.. image:: ../../_static/images/task_5.png
   :alt: 任务优先级就绪队列
   :align: center

**任务优先级就绪队列状态变化描述如下:**

1. 操作系统初始化时初始化每一级任务优先级就绪队列(只有控制节点),  并且对于需要自动
   启动的非周期任务和需要自动启动的无延迟启动时间的周期任务和IDLE任务,均需要将其插入任
   务优先级就绪队列尾部。

#. 调用启动任务接口启动处于休眠态的非周期任务和处于休眠态的无延迟启动时间的周期任务,会
   将该任务插入任务优先级就绪队列尾部。

#. 调用重启任务接口复位非周期任务和没有延迟启动时间的周期任务时将该任务插入任务优先级
   就绪队列尾部,如果该任务在被复位时处于就绪态那么需先将其从任务优先级就绪队列中移除。

#. 调用解挂任务接口解挂仅处于挂起态的任务时会将该任务插入任务优先级就绪队列尾部。

#. 调用资源等待接口以等待方式使任务等待资源时会将该任务从任务优先级就绪队列移除。

#. 任务等待的资源到来时,将此任务插入任务优先级就绪队列尾部。

#. 调用调用任务休眠接口使当前任务睡眠一段非零的时间时会将该任务从任务优先级就绪队列移除。

#. 调用停止任务接口停止处于就绪态的任务时会将该任务从任务优先级就绪队列移除。

#. 调用挂起任务接口挂起处于就绪态的任务时会将该任务从任务优先级就绪队列移除。

#. 周期等待接口使周期任务等待下个周期到来时,会将该任务从任务优先级就绪队列移除。

#. 系统定时器进行周期任务周期等待队列的时间通知时,将周期到来的就绪周期任务插入任务优
   先级就绪队列尾部。

#. 系统定时器进行周期任务截止时间等待队列时间通知时,如果截止时间到来的完成等待态的周
   期任务等待下一个周期到来的时间为零,将该任务插入任务优先级就绪队列尾部;否则将就绪周期
   任务从任务优先级就绪队列移除。

#. 系统定时器进行tick等待时间通知时,将需要从任务tick等待队列唤醒的任务插入到任务优先级
   就绪队列尾部。

#. 系统定时器进行任务的时间片轮转通知时,如果当前任务的时间片用完,并且该任务允许抢占、
   当前任务允许时间片调度、有相同优先级的就绪任务,则首先将该任务从就绪队列移除,然后再将任
   务重新插入到任务优先级就绪队列尾部。

任务tick等待队列
^^^^^^^^^^^^^^^^

任务管理模块使用任务tick等待队列管理等待时间到来的任务。

任务管理模块使用任务tick等待队列管理等待时间的任务。

任务tick等待队列中任务的等待时间按照升序排列,队列中每个任务中存储与前驱任务等待时间的
差值,每个任务的等待时间为前面所有节点和自身节点中存储差值的总和。这样的数据结构便于
tick中断到来时更新任务的等待时间,当tick中断到来时,仅需更新第一个节点的等待时间。而当
任务等待时间为0时,仅需移出等待时间为0 的任务,而不是遍历整个tick等待队列中所有任务。

任务tick等待队列采用双向链表存储结构。例如,如果存在几个处于tick等待状态的任务,并且它
们的等待时间分别是 1 tick、3 ticks、5 ticks、7 ticks、9 ticks,那么任务tick等待队列如图所示:

.. image:: ../../_static/images/task_6.png
   :alt: 任务tick等待队列
   :align: center

**任务tick等待队列状态变化描述如下:**

1. 操作系统初始化时将任务tick等待队列初始化为空(只有控制节点) 。

#. 调用调用任务休眠接口使任务休眠一段时间时,会将任务插入到任务tick等待队列。

#. 调用资源等待接口以等待方式使任务等待资源有限时间时,会将任务插入到任务tick等待队列。

#. 调用重启任务接口复位指定任务,若任务是计时等待状态,会将任务从任务tick等待队列移除。

#. 调用停止任务接口停止指定任务,若任务是计时等待状态,会将任务从任务tick等待队列移除。

#. 任务在有限时间等待的资源到来时,会将该任务从任务tick等待队列移除。

#. 系统定时器进行tick等待时间通知时,若任务tick等待队列中有等待时间为0的任务,会将
   任务从任务tick等待队列移除。

周期任务周期等待队列
^^^^^^^^^^^^^^^^^^^

任务管理模块使用周期任务周期等待队列管理等待周期到来的周期任务。

周期任务周期等待队列按照任务等待周期到来的时间进行升序排列,队列的每个任务中存储与前驱任务
等待时间的差值,每个任务的等待时间为前面所有节点和自身节点中存储差值的总和。当在进行时间通
知时,所有周期任务的等待的时间均需要更新,使用差分时间链就可以通过更新第一个节点的等待时间
达到更新整条链上的所有节点的等待时间。而当任务的周期到来时,仅需移出等待时间为0 的任务,而
不是遍历整个周期任务周期等待队列中所有任务。

周期任务周期等待队列采用双向链表存储结构。例如,如果存在几个处于周期等待状态的任务,并且它
们等待周期到来的时间分别是1 tick、3 ticks、5 ticks、7 ticks、9 ticks,那么周期任务周期等待
队列如图所示:

.. image:: ../../_static/images/task_7.png
   :alt: 任务周期等待队列
   :align: center

**周期任务周期等待队列状态变化描述如下:**

1. 操作系统初始化时初始化周期任务周期等待队列(只有控制节点)。对于需要自动启动并且有延
   迟启动时间的周期任务,需要将其插入周期任务周期等待队列。

#. 调用启动任务接口启动处于休眠态并且有延迟启动时间的周期任务时将该任务插入周期任务周
   期等待队列。

#. 调用重启任务接口复位周期任务时,如果任务是处于周期等待态的任务,则先将该任务从周期
   任务周期等待队列移除,如果该任务延迟启动时间不为零,再将该任务插入周期任务周期等待队列。

#. 调用停止任务接口停止处于周期等待态任务时将该任务从周期任务周期等待队列移除。

#. 系统定时器进行周期任务周期等待队列时间通知时,将周期到来的周期任务从周期任务周期等
   待队列移除。

#. 系统定时器进行周期任务截止时间等待队列时间通知时,将截止期到来、已完成本周期内任务
   并且等待下一个周期到来时间不为0的周期任务插入周期任务周期等待队列。

周期任务截止时间等待队列
^^^^^^^^^^^^^^^^^^^^^^^

任务管理模块使用周期任务截止时间等待队列管理等待截止期到来的周期任务。

周期任务截止时间等待队列中任务的截止等待时间按照升序排列,队列中每个周期任务中存储与前驱周
期任务截止等待时间的差值,每个周期任务的截止等待时间为前面所有节点和自身节点中存储差值的总
和。这样的数据结构便于在设置截止期通知时间时只需根据队列中第一个节点的等待时间进行设置,而
不是遍历整队列中所有节点。

周期任务截止时间等待队列采用双向链表存储结构。例如,如果存在几个处于等待截止时间到来的周期
任务,并且它们的截止等待时间分别是1 tick、3 ticks、5 ticks、7 ticks、9 ticks,那么周期任务
截止时间等待队列如图所示:

.. image:: ../../_static/images/task_8.png
   :alt: 周期任务截止时间等待队列
   :align: center

**周期任务截止时间等待队列状态变化描述如下:**

1. 操作系统初始化时将周期任务截止时间等待队列初始化为空(只有控制节点),对于自动启动且
   没有启动延迟的周期任务,会将任务插入到周期任务截止时间等待队列。

#. 调用启动任务接口,如果任务是周期任务并且没有延迟启动时间,会将该任务插入到周期任务
   截止时间等待队列。

#. 调用停止任务接口,若任务是周期任务并且处于等待截止期的到来,会将会将任务从周期任务
   截止时间等待队列移除。

#. 调用重启任务接口:
    
    1) 如果任务是周期任务并且等待截止期的到来,首先会将任务从周期任务截止时间等待队列
       移除。
    
    #) 如果任务是周期任务并且有延迟启动时间,会将任务插入到周期任务截止时间等待队列。

#. 系统定时器进行周期任务周期等待队列时间通知时,将周期到来的周期任务插入到周期任务截
   止时间等待队列。

#. 系统定时器进行周期任务截止时间等待队列时间通知时:
    
    1) 首先将需要通知的任务从周期任务截止时间等待队列中移除。
    
    #) 周期等待时间为0时,会重新插入到周期任务截止时间等待队列。

一旦周期任务被启动,要么等待周期的到来,要么等待截止期的到来,所以周期任务要么在周期任务周
期等待上,要么在周期任务截止时间等待队列上。

数据结构
~~~~~~~~

任务配置
--------

.. code-block:: c
    :caption: T_TTOS_TaskControlBlock
    :linenos:

    typedef struct T_TTOS_TaskInfo_struct {
        /* 任务ID */
        T_ULONG id;
        /* 任务名字 */
        T_UBYTE name[TTOS_OBJECT_NAME_LENGTH + 1];
        /* 任务的优先级,0－31级,0最高,31最低 */
        T_UBYTE taskPriority;
        /* 任务状态 */
        T_UWORD state;
        /* 任务入口函数 */
        T_TTOS_TaskRoutine entry;
        /* 等待的条件信息 */
        T_UWORD option;
        /* 等待的对象名称 */
        T_UBYTE waitObjName[TTOS_OBJECT_NAME_LENGTH + 1];
        /* 等待对象ID号 */
        T_VOID *waitObjId;
        /* 任务已经执行的tick数*/
        T_UDWORD executedTicks;
        /*表示任务绑定运行在哪个CPU上 ,CPU_NONE表示此任务是非绑定运行任务*/
        T_UWORD affinityCpuIndex;
        /*此属性仅对运行任务有效,表示任务运行在哪个CPU上,CPU_NONE表示此任务是非运行任务*/
        T_UWORD cpuIndex;
    } T_TTOS_TaskInfo;

任务控制块
----------

任务控制块是每个任务的核心数据,包含了任务的所有信息

.. code-block:: c
    :caption: T_TTOS_TaskControlBlock
    :linenos:

    typedef struct T_TTOS_TaskControlBlock_Struct {
        /* 管理对象 */
        T_TTOS_ObjectCore objCore;

        TASK_ID taskControlId;

        pid_t tid;
        /* 任务状态 */
        T_UWORD state;
        /* 任务的优先级,0－31级,0最高,31最低 */
        T_UBYTE taskPriority;
        /* 任务调度策略 */
        T_UBYTE taskSchedPolicy;
        /* 任务正在使用的优先级,0－31级,0最高,31最低 */
        T_UBYTE taskCurPriority;
        /* 0表示可抢占,非0表示不可抢占 */
        T_UWORD preempted;
        /* 任务初始可抢占属性,在配置时确定,运行时不会改变,0表示可抢占,非0表示不可抢占 */
        T_UWORD preemptedConfig;
        /* 任务运行时的参数,传递给任务入口函数entry使用 */
        T_VOID *arg;
        /* 任务入口函数 */
        T_TTOS_TaskRoutine entry;
        /* 等待信息控制结构 */
        T_TTOS_Task_Wait_Information wait;
        /* 任务事件句柄 */
        T_TTOS_EventControl *eventCB;
        /* 事件的等待节点 */
        struct list_node event_wait_node;
        /* 任务类型,分为周期任务和非周期任务 */
        T_TTOS_TaskType taskType;
        /* 任务休眠或者等待资源的时间*/
        T_UWORD waitedTicks;
        /* 任务时间片的初始大小值,单位为tick,在配置时确定,运行时不会改变 */
        T_UWORD tickSliceSize;
        /* 任务时间片的大小,单位为tick */
        T_UWORD tickSlice;
        /* 周期任务节点 */
        T_TTOS_PeriodTaskNode periodNode;
        /* 任务的资源节点 */
        T_TTOS_ResourceTaskNode resourceNode;
        /* 任务上下文 */
        T_TBSP_TaskContext switchContext;
        /* 任务运行栈的起始地址 */
        T_UBYTE *stackStart;
        /* 任务最初始的栈大小,进行任务栈保护后,此大小大于任务实际可以使用的栈大小。*/
        T_UWORD initialStackSize;
        /* 任务运行栈栈底 */
        T_UBYTE *stackBottom;
        /* 核心态栈 */
        T_UBYTE *kernelStackTop;
        /* 任务拥有的资源数 */
        T_UWORD resourceCount;
        /* 任务已经执行的tick数*/
        T_UDWORD executedTicks;
        /*是否在TTOS_ExitTaskHook()中释放任务栈 */
        T_BOOL isFreeStack;
        T_WORD taskErrno;
        /* 任务是否禁止删除的,任务拥有资源时,是不允许被删除的 */
        T_UWORD disableDeleteFlag;
        /* 任务是否已经被其他任务join */
        T_UWORD joinedFlag;
        /* 任务的多核信息 */
        T_TTOS_SmpInfo smpInfo;
        /* 进程句柄 */
        void *ppcb;
        /* try 的上下文 */
        struct list_head try_ctx_list;
        /* time event等待队列私有数据 */
        void *wq_priv;
        /* time event等待队列节点 */
        struct list_head wq_head;
        /* time event任务退出回调 */
        void (*wq_cleanup) (struct T_TTOS_TaskControlBlock_Struct *);
    } T_TTOS_TaskControlBlock;

TASK_ID作为任务控制块的抽象,为接口的一致性提供了支持。

.. code-block:: c
    :caption: TASK_ID
    :linenos:
    
    typedef struct T_TTOS_TaskControlBlock_Struct *TASK_ID;

栈信息结构
----------

.. code-block:: c
    :caption: T_TTOS_TaskControlBlock
    :linenos:

    typedef struct {
        /* 任务运行栈基地址 */
        T_UBYTE *stackBase;
        /* 任务运行栈底地址 */
        T_UBYTE *stackBottom;
        /* 任务运行栈的起始地址 */
        T_UBYTE *stackStart;
        /* 任务运行栈的大小 */
        T_ULONG stackSize;
        /* 任务的当前运行栈 */
        T_UBYTE *currentStack;
        /* 任务运行时使用的最大栈的大小 */
        T_ULONG stackMaxUseSize;
        /* 栈是否上溢 */
        T_BOOL isStackOverflowed;
        /* 栈是否下溢 */
        T_BOOL isStackUnderflowed;
        /* 是否在TTOS_ExitTaskHook()中释放任务栈 */
        T_BOOL isFreeStack;
    } T_TTOS_TaskStackInformation;