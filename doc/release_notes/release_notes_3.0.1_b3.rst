Intewell-RTOS v3.0.1_b3
=========================

发布日期: 2025年7月16日

版本特性
-----------------------

系统:

- 添加slub内存分配算法(不是默认不可测试)
- 修复futex的重大问题, 包括PI锁等
- 添加rw信号量支持(无法测试)
- 修复poll_notify导致内存溢出的问题(无法测试)
- 添加SDK生成时的描述文件
- 小部分重构signal相关的接口,以解决中断中发送信号导致异常的问题(无法测试)
- 修复概率多线程进程退出时发生异常的情况
- 添加内核对象引用计数机制用于安全释放复杂对象结构(无法测试)


已知问题
-----------------------

- [`RTOS-689 <http://*************:8080/browse/RTOS-689>`_]:  【新增问题】在实时串口shell终端输入tail -f /dev/kmsg，概率系统产生User Exception
- [`RTOS-687 <http://*************:8080/browse/RTOS-687>`_]:  【新增问题】fix_map_set函数中cache_clear操作仍会异常，偶现
- [`RTOS-686 <http://*************:8080/browse/RTOS-686>`_]:  【新增问题】用户程序使用&符号让其后台运行，运行一段时间后，整个系统卡死，ssh无法连接
- [`RTOS-685 <http://*************:8080/browse/RTOS-685>`_]:  【新增问题】timer_event_start2接口中list_add_before访问了0地址，导致系统挂掉
- [`RTOS-684 <http://*************:8080/browse/RTOS-684>`_]:  【新增问题】后台启动多个简单应用(100个左右)，busybox命令不能使用
- [`RTOS-683 <http://*************:8080/browse/RTOS-683>`_]:  【新增问题】用户态和内核的错误码使用的一个范围，内核部分地方会将两种错误码进行转换，没有检测机制
- [`RTOS-682 <http://*************:8080/browse/RTOS-682>`_]:  【新增问题】用户应用程序ctrl+C后，用户态shell没有反应，使用shh连接上去后，用户程序退出。串口有输出。偶现
- [`RTOS-680 <http://*************:8080/browse/RTOS-680>`_]:  【新增问题】飞腾E2000D，开启kasan后，ctrl C出现异常，必现
- [`RTOS-675 <http://*************:8080/browse/RTOS-675>`_]:  【新增问题】pthread_getperiodcompletetime返回值错误
- [`RTOS-674 <http://*************:8080/browse/RTOS-674>`_]:  【新增问题】pthread_getperiodcount返回值不正确
- [`RTOS-665 <http://*************:8080/browse/RTOS-665>`_]:  【新增问题】执行shell脚本整机卡死
- [`RTOS-663 <http://*************:8080/browse/RTOS-663>`_]:  【新增问题】在串口输入ifconfig会出现多几个字符的情况
- [`RTOS-662 <http://*************:8080/browse/RTOS-662>`_]:  【新增问题】偶现系统启动崩溃异常
- [`RTOS-613 <http://*************:8080/browse/RTOS-613>`_]:  【新增问题】shell测试套件testsuite执行runtest失败
- [`RTOS-645 <http://*************:8080/browse/RTOS-645>`_]:  "【新增问题】在userAppInit中直接代码while(1)- [` <http://*************:8080/browse/>`_]:  下载运行后，在串口输入ps，串口卡死"
- [`RTOS-619 <http://*************:8080/browse/RTOS-619>`_]:  【新增问题】实时系统创建第12个线程或进程失败，提示 Out of memory或No such file or directory
- [`RTOS-654 <http://*************:8080/browse/RTOS-654>`_]:  【新增问题】调用pthread_attr_setinheritsched 设置 PTHREAD_INHERIT_SCHED属性，创建任务未继承父任务属性
- [`RTOS-650 <http://*************:8080/browse/RTOS-650>`_]:  【新增问题】创建同优先级两个任务进行消息队列通信，通信失败
- [`RTOS-644 <http://*************:8080/browse/RTOS-644>`_]:  PSE54移植
- [`RTOS-588 <http://*************:8080/browse/RTOS-588>`_]:  【新增问题】Eigen库异常用例
- [`RTOS-637 <http://*************:8080/browse/RTOS-637>`_]:  【新增问题】由于实时性优化关闭syslogd 导致日志无法存储到文件
- [`RTOS-636 <http://*************:8080/browse/RTOS-636>`_]:  【新增问题】由于优化实时性，实时top命令不可用，显示值都是0.0%
- [`RTOS-635 <http://*************:8080/browse/RTOS-635>`_]:  【新增问题】ssh连上实时输入reboot产生User Exception
- [`RTOS-587 <http://*************:8080/browse/RTOS-587>`_]:  【新增问题】boost thread库异常用例
- [`RTOS-623 <http://*************:8080/browse/RTOS-623>`_]:  【新增问题】使用ssh连接shell，执行boost用例卡死，1分钟后，ssh的shell断掉，串口打印coredump错误
- [`RTOS-615 <http://*************:8080/browse/RTOS-615>`_]:  【新增问题】实时串口shell执行内存申请释放用例，实时ip无法ping通
- [`RTOS-614 <http://*************:8080/browse/RTOS-614>`_]:  【新增问题】实时串口shell端执行ls testsuite，发现部分内容为空显示
- [`RTOS-600 <http://*************:8080/browse/RTOS-600>`_]:  【新增问题】在实时shell执行wget -c ftp://192.168.xx/123.py提示wget: read error: Invalid argument
- [`RTOS-538 <http://*************:8080/browse/RTOS-538>`_]:  【新增问题】局域网存在nc程序大量测试，实时进行反复fork同一个进程，实时ftp不可使用
- [`RTOS-101 <http://*************:8080/browse/RTOS-101>`_]:  增加CI对代码风格的校验
- [`RTOS-102 <http://*************:8080/browse/RTOS-102>`_]:  增加CI对内核功能的自动测试
- [`RTOS-594 <http://*************:8080/browse/RTOS-594>`_]:  【新增问题】fork一个子进程，子进程退出后再fork，发现反复fork285439次，提示Out of memory
- [`RTOS-430 <http://*************:8080/browse/RTOS-430>`_]:  "【新增问题】调用getitimer( ITIMER_REAL )提示Function not implemented"
- [`RTOS-426 <http://*************:8080/browse/RTOS-426>`_]:  【新增问题】gdbserver存在ctrl+c不能中止的情况。
- [`RTOS-428 <http://*************:8080/browse/RTOS-428>`_]:  【新增问题】存在reboot重启不成功的情况
- [`RTOS-597 <http://*************:8080/browse/RTOS-597>`_]:  【新增问题】在串口shel端执行用例并ctrl+c，ssh连上实时后输入reboot，系统产生User Exception
- [`RTOS-547 <http://*************:8080/browse/RTOS-547>`_]:  【新增问题】在arm32实时系统上执行c++测试程序，系统报异常
- [`RTOS-596 <http://*************:8080/browse/RTOS-596>`_]:  【新增问题】在实时shell端执行tail -f /proc/klog，发现有日志产生但未持续输出
- [`RTOS-389 <http://*************:8080/browse/RTOS-389>`_]:  【新增问题】测试调用aio_error()检查状态是ECANCELED，aio_return()返回-1，测试失败
- [`RTOS-564 <http://*************:8080/browse/RTOS-564>`_]:  【新增问题】aio_suspend暂停异步io操作，未进入信号处理程序中
- [`RTOS-572 <http://*************:8080/browse/RTOS-572>`_]:  【新增问题】测试当系统内存不足时，调用pthread_cond_init系统反复产生异常
- [`RTOS-573 <http://*************:8080/browse/RTOS-573>`_]:  "【新增问题】执行pthread_cond_signal_1_2用例系统提示Assertion failed: ""No memory"" && 0"
- [`RTOS-550 <http://*************:8080/browse/RTOS-550>`_]:  【新增问题】ps无法查看zombie状态的进程
- [`RTOS-480 <http://*************:8080/browse/RTOS-480>`_]:  【新增问题】clock_settime设置系统时间为过去时间，调用nanosleep未失败
- [`RTOS-327 <http://*************:8080/browse/RTOS-327>`_]:  【新增问题】系统存在内存泄露
- [`RTOS-412 <http://*************:8080/browse/RTOS-412>`_]:  【新增问题】实时系统线程优先级划分及优先级策略，优先级>周期>时间片未实现
- [`RTOS-413 <http://*************:8080/browse/RTOS-413>`_]:  【新增需求】目前系统无法查看系统资源使用情况
- [`RTOS-446 <http://*************:8080/browse/RTOS-446>`_]:  【新增问题】sem_unlink删除信号量SEM_NAME，sem_open创建SEM_NAME引用一个新的信号量 ，sem_getvalue获取该信号量值不正确
- [`RTOS-485 <http://*************:8080/browse/RTOS-485>`_]:  【新增问题】当条件变量attr为NULL，其效果与默认条件变量属性对象的地址相同，测试失败
- [`RTOS-519 <http://*************:8080/browse/RTOS-519>`_]:  【新增问题】当cpu上仅有idle任务时，top命令显示cpu占用率不正确
- [`RTOS-341 <http://*************:8080/browse/RTOS-341>`_]:  【新增问题】未触发xfsz信号
- [`RTOS-342 <http://*************:8080/browse/RTOS-342>`_]:  【新增问题】未触发xcpu信号
- [`RTOS-379 <http://*************:8080/browse/RTOS-379>`_]:  【新增问题】创建定时器设置响应时间点，clock_settime设置系统为超过该时间点，定时器未响应
- [`RTOS-378 <http://*************:8080/browse/RTOS-378>`_]:  【新增问题】clock_settime设置系统为过去时间，clock_gettime获取时间不正确
- [`RTOS-283 <http://*************:8080/browse/RTOS-283>`_]:  【新增问题】tar命令报错
- [`RTOS-239 <http://*************:8080/browse/RTOS-239>`_]:  dup文件描述符后两个fd未共享offset
- [`RTOS-210 <http://*************:8080/browse/RTOS-210>`_]:  【新增问题】调用mbsinit确定转换对象状态失败
- [`RTOS-44 <http://*************:8080/browse/RTOS-44>`_]:  测试wmemcpy接口，连续调用 wmemcpy() 函数多次，未能够正确处理多次复制操作
- [`RTOS-288 <http://*************:8080/browse/RTOS-288>`_]:  【新增问题】执行测试栈溢出用例时，打印有错行
- [`RTOS-325 <http://*************:8080/browse/RTOS-325>`_]:  【新增问题】llvm测试用例不通过
- [`RTOS-208 <http://*************:8080/browse/RTOS-208>`_]:  【新增问题】调用mbrlen获取字符的字节数失败
- [`RTOS-207 <http://*************:8080/browse/RTOS-207>`_]:  【新增问题】宽字符问题mbrtowc、wcscat、wcscoll、wmemcpy、ungetwc、iswprint接口功能不正确
- [`RTOS-209 <http://*************:8080/browse/RTOS-209>`_]:  【新增问题】测试iswgraph() 函数来检查给定的宽字符是否为可打印字符并且不是空格失败
- [`RTOS-41 <http://*************:8080/browse/RTOS-41>`_]:  测试wcscoll接口进行字符比较失败
- [`RTOS-38 <http://*************:8080/browse/RTOS-38>`_]:  传参ch5 = L'\u03A9';  // Unicode Ω (希腊字母大写omega)，iswgraph未返回非0
- [`RTOS-39 <http://*************:8080/browse/RTOS-39>`_]:  传参 ch5 = L'\u03A9';  // Unicode Ω (希腊字母大写omega)，iswprint未返回0
- [`RTOS-76 <http://*************:8080/browse/RTOS-76>`_]:  测试munmap函数提示：Operation not permitted
- [`RTOS-518 <http://*************:8080/browse/RTOS-518>`_]:  【新增问题】top命令中显示cpu使用率各项相加不为100%
- [`RTOS-521 <http://*************:8080/browse/RTOS-521>`_]:  【新增问题】创建多个进程绑在不同cpu上，每个进程进行不同程度的负载计算，top查看该进程cpu占用率不正确
- [`RTOS-83 <http://*************:8080/browse/RTOS-83>`_]:  调用fdatasync，返回error: Function not implemented
- [`RTOS-77 <http://*************:8080/browse/RTOS-77>`_]:  使用 msync() 来将修改的数据同步到文件中，导致系统报异常
- [`RTOS-129 <http://*************:8080/browse/RTOS-129>`_]:  【新增问题】通过localeconv函数返回negative_sign信息失败
- [`RTOS-130 <http://*************:8080/browse/RTOS-130>`_]:  【新增问题】通过setlocale设置，当category为LC_TIME，locale为POSIX时设置失败
- [`RTOS-131 <http://*************:8080/browse/RTOS-131>`_]:  【新增问题】通过localeconv函数返回p_sep_by_space信息错误
- [`RTOS-132 <http://*************:8080/browse/RTOS-132>`_]:  【新增问题】通过localeconv函数返回int_n_sep_by_space信息不正确
- [`RTOS-153 <http://*************:8080/browse/RTOS-153>`_]:  【新增问题】通过localeconv函数返回frac_digits信息不正确
- [`RTOS-154 <http://*************:8080/browse/RTOS-154>`_]:  【新增问题】通过localeconv函数返回int_frac_digits信息不正确
- [`RTOS-155 <http://*************:8080/browse/RTOS-155>`_]:  【新增问题】调用setlocale函数进行设置，localeconv获取thousands_sep格式信息不正确
- [`RTOS-156 <http://*************:8080/browse/RTOS-156>`_]:  【新增问题】通过localeconv函数返回int_p_sep_by_space信息、mon_thousands_sep信息、p_cs_precedes信息不正确
- [`RTOS-157 <http://*************:8080/browse/RTOS-157>`_]:  【新增问题】通过localeconv函数返回int_p_sign_posn信息、mon_decimal_point信息、thousands_sep信息不准确
- [`RTOS-158 <http://*************:8080/browse/RTOS-158>`_]:  【新增问题】通过localeconv函数返回currency_symbol信息、int_n_cs_precedes信息、int_curr_symbol信息不准确
- [`RTOS-159 <http://*************:8080/browse/RTOS-159>`_]:  【新增问题】通过localeconv函数返回n_sep_by_space信息、p_sign_posn信息、n_sign_posn信息、int_p_cs_precedes信息、int_n_sign_posn信息不准确
- [`RTOS-160 <http://*************:8080/browse/RTOS-160>`_]:  【新增问题】通过localeconv函数返回n_cs_precedes信息不正确
- [`RTOS-161 <http://*************:8080/browse/RTOS-161>`_]:  【新增问题】locate为无效时，setlocale函数未返回NULL
- [`RTOS-162 <http://*************:8080/browse/RTOS-162>`_]:  【新增问题】setlocale设置本地环境后strcoll比较字符串顺序不正确
- [`RTOS-163 <http://*************:8080/browse/RTOS-163>`_]:  【新增问题】setlocale函数未返回当前活动locale的全名称
- [`RTOS-188 <http://*************:8080/browse/RTOS-188>`_]:  【新增问题】测试子进程不会继承文件锁，测试失败
- [`RTOS-199 <http://*************:8080/browse/RTOS-199>`_]:  【新增问题】调用munmap提示munmap: Operation not permitted
- [`RTOS-198 <http://*************:8080/browse/RTOS-198>`_]:  【新增问题】调用mmap提示mmap: Operation not permitted
- [`RTOS-46 <http://*************:8080/browse/RTOS-46>`_]:  传递 NULL 指针时，wmemmove() 函数未出现异常



问题修复列表
-----------------------

- [`RTOS-661 <http://*************:8080/browse/RTOS-661>`_]:  【长沙时代通号】reboot执行卡死
- [`RTOS-671 <http://*************:8080/browse/RTOS-671>`_]:  【新增问题】异常未产生coredump文件
- [`RTOS-672 <http://*************:8080/browse/RTOS-672>`_]:  【新增问题】代码段包含产生异常，但是未生成对应的coredump文件
- [`RTOS-670 <http://*************:8080/browse/RTOS-670>`_]:  【新增问题】Segmentation fault未产生coredump文件
- [`RTOS-668 <http://*************:8080/browse/RTOS-668>`_]:  【新增问题】访问代码段，未产生coredump，并且shell卡死
- [`RTOS-669 <http://*************:8080/browse/RTOS-669>`_]:  【新增问题】不应该不断打印提示符
- [`RTOS-688 <http://*************:8080/browse/RTOS-688>`_]:  【新增问题】在实时shell终端执行sleep 30后，键入ctrl+z无法挂起该作业
- [`RTOS-690 <http://*************:8080/browse/RTOS-690>`_]:  【新增问题】vscode中加载BSP失败
- [`RTOS-691 <http://*************:8080/browse/RTOS-691>`_]:  【新增问题】在arm32位上测试解除阻塞在条件变量的所有任务时，系统产生Kernel Exception
- [`RTOS-681 <http://*************:8080/browse/RTOS-681>`_]:  【新增问题】接收网络包调用kpoll_notify的过程中操作信号量时拿到了空的信号量导致崩溃，偶现
- [`RTOS-692 <http://*************:8080/browse/RTOS-692>`_]:  【新增问题】iperf3测试tcp：rtos作为服务端，实时系统产生异常
- [`RTOS-676 <http://*************:8080/browse/RTOS-676>`_]:  【新增问题】ssh连接到虚拟机产生异常

提交日志
-----------------------

- [`d7d26fb5 <http://************/intewell-os/ttos/ttos-n/commit/d7d26fb57daf2038c96edda2de05692b2afe9700>`_]: Merge branch 'zyh' into 'master'
- [`b4d6e6f2 <http://************/intewell-os/ttos/ttos-n/commit/b4d6e6f26270364ee14e33552355bc6057fc3fb6>`_]: Merge branch 'zyh' into 'master'
- [`bb2ba27a <http://************/intewell-os/ttos/ttos-n/commit/bb2ba27a43e888d28c461a61df3e3c932882d04c>`_]: Merge branch 'zyh' into 'master'
- [`14776147 <http://************/intewell-os/ttos/ttos-n/commit/1477614721ca9092350afcf010f830d977feac32>`_]: wql要求的删除wql遗留的调试代码
- [`160b51eb <http://************/intewell-os/ttos/ttos-n/commit/160b51ebbfc481e75042bd22628547062e7f54ff>`_]: 处理当检查到pc或栈非法时未进行信号检查的问题
- [`fe8dcd7e <http://************/intewell-os/ttos/ttos-n/commit/fe8dcd7e2f0bcdd66df3ebe632b818482f29ac3b>`_]: 修复非ptrace下 遇到断点时应跳过断点指令返回
- [`db3f6f0d <http://************/intewell-os/ttos/ttos-n/commit/db3f6f0d7f2d64b70cd4778b7b9f01ecd03fb539>`_]: 添加SDK生成时的描述文件
- [`312e84c3 <http://************/intewell-os/ttos/ttos-n/commit/312e84c38d434f651d1fc1710eaf3d24d13d49b5>`_]: 修复了 pcb_waker->exit_signal 为0 时 判断 is_stop_set 导致异常
- [`80bd0826 <http://************/intewell-os/ttos/ttos-n/commit/80bd08263b65d0731fa93d551edfc9784f5f59ed>`_]: Merge branch 'zyh' into 'master'
- [`90eaf110 <http://************/intewell-os/ttos/ttos-n/commit/90eaf1108fb5bd6a390883df827d8104982606dd>`_]: 修复x86 构建失败的问题
- [`d143d9f3 <http://************/intewell-os/ttos/ttos-n/commit/d143d9f364d5f5bb2f0af0aa68b6565cc5c1cb87>`_]: 修改默认的内存分配算法恢复为tlsf
- [`ebe2b20c <http://************/intewell-os/ttos/ttos-n/commit/ebe2b20c34bdc9a52402883fa954919b5d44358b>`_]: 修复 可能的线程退不出的问题
- [`56ec1dd7 <http://************/intewell-os/ttos/ttos-n/commit/56ec1dd73e9c146c501e4ba56e0658096c2c5310>`_]: 修复错误的发送了信号值
- [`cb9570c1 <http://************/intewell-os/ttos/ttos-n/commit/cb9570c10c0dd03e0f7be0469a68194ef8b330cb>`_]: 添加内核对象引用计数机制用于安全释放复杂对象结构
- [`73ad7a27 <http://************/intewell-os/ttos/ttos-n/commit/73ad7a277869e233b56371d69e6e4d3170db14c0>`_]: 在path中添加malloc内存分配检查 避免无内存时发生异常
- [`3162ec35 <http://************/intewell-os/ttos/ttos-n/commit/3162ec35a7a0b4e0bfbff1882432f1663f753128>`_]: Merge branch 'zyh' into 'master'
- [`74ac3d64 <http://************/intewell-os/ttos/ttos-n/commit/74ac3d642cf04b2a7f191a44dfdb953517eaf406>`_]: 添加。kernel_signal_kill_with_worker接口异步发送信号 解决 exit_task 中开锁导致链表被破坏 发送异常的情况
- [`39350183 <http://************/intewell-os/ttos/ttos-n/commit/39350183f5b476080c1df025edd6acfe60efbcc8>`_]: Merge branch 'zyh' into 'master'
- [`247540b1 <http://************/intewell-os/ttos/ttos-n/commit/247540b1f79ebf38e4bb297212f88221d4067070>`_]: Merge branch 'zyh' into 'master'
- [`6045c7b1 <http://************/intewell-os/ttos/ttos-n/commit/6045c7b142d52becb21db4800b4d6bd3346276a6>`_]: Merge branch 'zyh' into 'master'
- [`9f4f6fa7 <http://************/intewell-os/ttos/ttos-n/commit/9f4f6fa782b3f1bac3b4256c4f1646aa2049e1c5>`_]: 修复 slub。free_slab_page时可能出现assert的问题
- [`c908e687 <http://************/intewell-os/ttos/ttos-n/commit/c908e6870490c66d530354f9930e84e2bab810b3>`_]: 修改默认的KERNEL_HEAP_SIZE 值 避免切换回tlsf发生错误
- [`dbc76844 <http://************/intewell-os/ttos/ttos-n/commit/dbc76844b4166c88e9c3ad8aea405398a2e0b98a>`_]: 修复slub的情况下 通过kfree释放页时 可能造成异常的问题
- [`f4eb06cf <http://************/intewell-os/ttos/ttos-n/commit/f4eb06cfd4e03843e9dc6ef4645e4cb8fb0ba7c9>`_]: 修复。alloc_from_page多核可能产生异常的问题
- [`8fc43ddb <http://************/intewell-os/ttos/ttos-n/commit/8fc43ddb3468a5bc6b54aa926f98955c373b24c3>`_]: 修改 signal的接口 统一为kernel_signal_kill 并使用wq解决中断发送信号时无锁导致出现异常的情况
- [`e60e227d <http://************/intewell-os/ttos/ttos-n/commit/e60e227dcfc9b109b4e1cc749d9eea90da03fc9c>`_]: 移除错误提交的ubi子模块
- [`2fb8ac4c <http://************/intewell-os/ttos/ttos-n/commit/2fb8ac4cbce6d8363f2e971dc2e37527c4bd786a>`_]: 添加S5000c网卡接收任务绑定CPU0
- [`68a0e25d <http://************/intewell-os/ttos/ttos-n/commit/68a0e25df9e61d00b8c08a4416199336b7c29d34>`_]: 兼容S5000C的从核启动代码
- [`fc0f7eca <http://************/intewell-os/ttos/ttos-n/commit/fc0f7ecafa7216fec77b35d1edcd20abfec1c817>`_]: 添加 slub对构造函数的支持
- [`842d547e <http://************/intewell-os/ttos/ttos-n/commit/842d547e9dfd59517eb52aa1c767342627962e58>`_]: 添加div_u64宏
- [`34d88a85 <http://************/intewell-os/ttos/ttos-n/commit/34d88a85233c6217bfcae228f0da186b85d84029>`_]: 修复slub下 free命令显示内存大小错误
- [`8f6a7199 <http://************/intewell-os/ttos/ttos-n/commit/8f6a7199b9f0d848c2cce34676f3af93dac04edd>`_]: Merge branch 'zyh' into 'master'
- [`6e403245 <http://************/intewell-os/ttos/ttos-n/commit/6e403245264b5ce646fd0dcae26c13d55c3a25cb>`_]: 添加slub 内存分配算法并作为默认
- [`53f37caf <http://************/intewell-os/ttos/ttos-n/commit/53f37caf9be002fe7f780547c3cfb12d8fd1d635>`_]: Merge branch 'kpoll' into 'master'
- [`4fd2e48b <http://************/intewell-os/ttos/ttos-n/commit/4fd2e48b87e83b6094dd723e8134a6ef34b2be62>`_]: Merge branch 'cherry-pick-1ce52d2d' into 'master'
- [`b4fc2644 <http://************/intewell-os/ttos/ttos-n/commit/b4fc2644f5be3a4c1142b5040911cf01a6efaa33>`_]: 添加mm_region_map通过mm_region_list的方式获取va，添加map外部函数锁
- [`4d390292 <http://************/intewell-os/ttos/ttos-n/commit/4d390292e88e06fb881593a6e481b7f6101894e0>`_]: 操作kpollfd时关调度
- [`9109e753 <http://************/intewell-os/ttos/ttos-n/commit/9109e753df83eb6faec6aecc351c635af4ebf749>`_]: 更新libfs
- [`4df92453 <http://************/intewell-os/ttos/ttos-n/commit/4df92453129eeac61f3927919d1091727dcc5835>`_]: 在poll 建立和销毁时关闭调度器
- [`b72ae3a3 <http://************/intewell-os/ttos/ttos-n/commit/b72ae3a3f62cdaff2c1cd87b485ad821b3542e76>`_]: Merge branch 'sunx_develop' into 'master'
- [`76985e34 <http://************/intewell-os/ttos/ttos-n/commit/76985e3459a0fb99517dbaec11f070bff18939dc>`_]: 1.packet_input()中仅当套接字被设置了多路复用时才调用kpoll_notify() 2.AF_PACKET取消设置多路复用时将套接字结构体中的成员变量kfd置空
- [`3d445142 <http://************/intewell-os/ttos/ttos-n/commit/3d445142ebb92d97be2d1c4fb62c30bfacb4a4b3>`_]: Merge branch 'sunx_develop' into 'master'
- [`eb9af36a <http://************/intewell-os/ttos/ttos-n/commit/eb9af36a2781d232f88d2f1b293bea230a0c67b7>`_]: 1.增加LwIP内存池与消息队列的大小，避免在网络高负载时出现内存结构用完和消息队列排满的情况 2.如果配置绑定网卡接收任务，则同时绑定LwIP主任务到同一处理器核心
- [`f3a6cb92 <http://************/intewell-os/ttos/ttos-n/commit/f3a6cb929e5c9481d6b2160988e04d78fefb77e5>`_]: 减少网络收发包时的一次数据拷贝
- [`ba47e93e <http://************/intewell-os/ttos/ttos-n/commit/ba47e93ed68b5e12190a8305cfd905b8a3588b6e>`_]: Merge branch 'zyh' into 'master'
- [`0193ed74 <http://************/intewell-os/ttos/ttos-n/commit/0193ed7450d6c5b4e4195e1e9bbcf82f90b5a488>`_]: Merge branch 'maoyz_fix_0701' into 'master'
- [`b0f69653 <http://************/intewell-os/ttos/ttos-n/commit/b0f6965349742088b99b190818e1159b9d7979dc>`_]: 修复格式化文件后发生的构建错误
- [`d01a2a98 <http://************/intewell-os/ttos/ttos-n/commit/d01a2a9864f26e24936afbd8e08e1ef0239889a9>`_]: 1.修改部分注释 2.waitqueue_sleep_with_handler恢复自旋锁
- [`018d63d3 <http://************/intewell-os/ttos/ttos-n/commit/018d63d3c73d66f1543d86d0f00b74a26d5610a2>`_]: Merge branch 'zyh' into 'master'
- [`bfa06e56 <http://************/intewell-os/ttos/ttos-n/commit/bfa06e561d0f5a08249a03e0fa99815f9c53a99a>`_]: 修改PCAP抓包工具使用自定义的AF_PCAP套接字(临时方案)
- [`d2043b33 <http://************/intewell-os/ttos/ttos-n/commit/d2043b337eb0ec091e3c89d61a4dddf1c5694b84>`_]: Merge branch 'sunx_develop' into 'master'
- [`2a95fe24 <http://************/intewell-os/ttos/ttos-n/commit/2a95fe24fb0466a1bf1f1711cb71b1ecceee9d2f>`_]: Merge branch 'zyh' into 'master'
- [`e93e609b <http://************/intewell-os/ttos/ttos-n/commit/e93e609bc1d0e1f4b1a7db9d50fea77a966bed10>`_]: Merge branch 'maoyz_fix_1' into 'master'
- [`a5298d6c <http://************/intewell-os/ttos/ttos-n/commit/a5298d6cfa0a4cfef92c6c310c172f1ea60cea93>`_]: 添加。cpu_to_be64 支持
- [`bcf0cc20 <http://************/intewell-os/ttos/ttos-n/commit/bcf0cc20e944a06ffc7786425541a6db014c50bd>`_]: 修复一些头文件问题
- [`38b93883 <http://************/intewell-os/ttos/ttos-n/commit/38b93883f94155d3d58a51d5aba8e50aaf99e28c>`_]: 添加。uninitialized_var fallthrough 宏
- [`a875f11a <http://************/intewell-os/ttos/ttos-n/commit/a875f11a72d3a84d97a4fb8822bd03830811e3f0>`_]: 添加 log_print_hex_dump 接口 用于dump 数据
- [`f52e3847 <http://************/intewell-os/ttos/ttos-n/commit/f52e38472a280ba2684d5c0bf43f51c00c7ed37e>`_]: 添加 rw信号量支持
- [`5a4aec9b <http://************/intewell-os/ttos/ttos-n/commit/5a4aec9bb423691643c199b8aa03f0f6f04e5c4d>`_]: 1.menuconfig中配置网卡接收任务绑核时要依赖多核配置 2.网卡接收绑核时在任务名中显示核心号
- [`458b873f <http://************/intewell-os/ttos/ttos-n/commit/458b873f082b8ab7b179f30173ef217cc9b26d81>`_]: 解决编译问题
- [`915b9c73 <http://************/intewell-os/ttos/ttos-n/commit/915b9c73df9ba82c2d6765d0a8cdd84460e7def0>`_]: 1.解决timecounter_read实现有误，导致arm32定时器时间设置有误的问题 2.修改该函数名为timestamp_ns_get
- [`26e6349c <http://************/intewell-os/ttos/ttos-n/commit/26e6349c58c42faea4924e1e0f2ef4b4ee384910>`_]: Merge branch 'maoyz_0628' into 'master'
- [`9c0259a9 <http://************/intewell-os/ttos/ttos-n/commit/9c0259a9f07f9b14ce6aacae020f18e00f64a117>`_]: 1.解决ftp卡顿问题 2.恢复网卡收包任务默认不绑核
- [`d61a1135 <http://************/intewell-os/ttos/ttos-n/commit/d61a113537548acc9ec24f5dc82dea5d8d305485>`_]: Merge branch 'zyh' into 'master'
- [`5b03e6e2 <http://************/intewell-os/ttos/ttos-n/commit/5b03e6e25715e91d21490190e63e11c21a201b35>`_]: Merge branch 'update-release-sema-20250626' into 'master'
- [`58370f32 <http://************/intewell-os/ttos/ttos-n/commit/58370f32d5561868017840e4965b11a185f38a0f>`_]: 添加 tty的窗口大小变动通知
- [`878e068d <http://************/intewell-os/ttos/ttos-n/commit/878e068de6106a99367a5655386e1283c5a58ee6>`_]: 在尝试取出task之后，保证上层wakedup_task指针内容被正确设置
- [`00eda5e2 <http://************/intewell-os/ttos/ttos-n/commit/00eda5e2b827c36c88ea79d5677d681ab0baaf51>`_]: Merge branch 'maoyz_0626_1041' into 'master'
- [`aa0cbe8c <http://************/intewell-os/ttos/ttos-n/commit/aa0cbe8c00321ea28aa27a8900bf81a53644218c>`_]: 修复 kfutex_lock_pi kfutex_unlock_pi实现有误的问题
- [`c395f530 <http://************/intewell-os/ttos/ttos-n/commit/c395f530c28dc5feeff70f4fe223b8e571b708f7>`_]: 修复释放锁错误的问题
- [`ea53619c <http://************/intewell-os/ttos/ttos-n/commit/ea53619c6e0bbdef35e6023e293e01cd3287a974>`_]: 格式修改
- [`92e8e389 <http://************/intewell-os/ttos/ttos-n/commit/92e8e3897292868fa48b1dfae3dac7c2702fd877>`_]: 允许持锁退出,避免任务挂起，资源不能释放
- [`390b6b5b <http://************/intewell-os/ttos/ttos-n/commit/390b6b5b5790eba057031d7201b262d681190bb2>`_]: 1.添加可被中断的互斥锁 2.修复futex pi实现存在的错误
- [`16bca606 <http://************/intewell-os/ttos/ttos-n/commit/16bca6064bb6b99869303496a4dfe8f7afa3a9ac>`_]: Merge branch 'update-aarch64-atomic-20250625' into 'master'
- [`adc00ad8 <http://************/intewell-os/ttos/ttos-n/commit/adc00ad88b13142f4bd0b6c5c7edb13a7d5f0a02>`_]: 修复lwext4无法挂载大于32G磁盘的问题
- [`e951b3d9 <http://************/intewell-os/ttos/ttos-n/commit/e951b3d92cf96d84ac02b6cb2c4fe6bd49f97764>`_]: 修复信号量非优先级天花板也设置优先级的问题
- [`e6fbbfad <http://************/intewell-os/ttos/ttos-n/commit/e6fbbfad71a607b36da42eebfab7048051c75fd3>`_]: 修复kasan编译报错 添加lwip和libk的kasan参数
- [`5ee1f6c9 <http://************/intewell-os/ttos/ttos-n/commit/5ee1f6c98c0fbe7c83ac4b2649ae2321a6fae48b>`_]: 更新aarch64原子操作全部使用寄存器的低32位
- [`c6840c14 <http://************/intewell-os/ttos/ttos-n/commit/c6840c147e8bd8b94fac2b5b4a527502c3876686>`_]: Merge branch 'maoyz_0625' into 'master'
- [`8b08708a <http://************/intewell-os/ttos/ttos-n/commit/8b08708a8d5635319259ae2a123446f26243d5ea>`_]: Merge branch 'maoyz_0625' into 'master'
- [`4d401caf <http://************/intewell-os/ttos/ttos-n/commit/4d401cafca608e0587d597f4762db266013e19d8>`_]: Merge branch 'fix-futex' into 'master'
- [`9c7a4b23 <http://************/intewell-os/ttos/ttos-n/commit/9c7a4b2328c5c5d103ea9eef6af05811e058caab>`_]: 修复aarch64原子操作
- [`acc3e5e1 <http://************/intewell-os/ttos/ttos-n/commit/acc3e5e12d1718f30a276ec6a06b97b14081f4b1>`_]: 修复futex在上锁时发生竞争情况下，创建信号量初值问题
- [`053c36cc <http://************/intewell-os/ttos/ttos-n/commit/053c36cc831576bcdd0e6b826e67ef1ec438bc17>`_]: 修复重调度cpu可能选择错误的问题
- [`ffe6c5bf <http://************/intewell-os/ttos/ttos-n/commit/ffe6c5bf0e70e1b69c5817e9a5e0793fb3c3747e>`_]: 解决编译问题
- [`c62f7703 <http://************/intewell-os/ttos/ttos-n/commit/c62f7703d9475e2ded99f0854052ea6dfa35671a>`_]: 解决任务在退出过程中，销毁了一些资源，产生中断后可能又使用到这些资源，导致异常的问题
- [`cc25f7ed <http://************/intewell-os/ttos/ttos-n/commit/cc25f7ed6db2e93a1e15618baf5b9f7cfc57e991>`_]: Merge branch 'maoyz_0624' into 'master'
- [`e7c49975 <http://************/intewell-os/ttos/ttos-n/commit/e7c49975f69085a28c5f015b7b2a558538245905>`_]: 临时解决ftp延迟较大的问题
- [`4a713f07 <http://************/intewell-os/ttos/ttos-n/commit/4a713f072237c4a41b29a6f448e61d03b74b5283>`_]: Merge branch 'sunx_develop' into 'master'
- [`92e7fcd1 <http://************/intewell-os/ttos/ttos-n/commit/92e7fcd115e7c8056cca89d95cd5d4f8dc9b9d8a>`_]: Merge branch 'maoyz_bugifx_0623_1603' into 'master'
- [`e3676bce <http://************/intewell-os/ttos/ttos-n/commit/e3676bce40ba0540dc0d1bea48eb9c0ab04a1b08>`_]: 修复信号出入队时使用不同的锁的bug
- [`50abcee4 <http://************/intewell-os/ttos/ttos-n/commit/50abcee45d859459d560b8aed339729bf8e76733>`_]: Merge branch 'maoyz_0623_bugfix' into 'master'
- [`2a5fada1 <http://************/intewell-os/ttos/ttos-n/commit/2a5fada1ac96ddd08e1c3f95c1d12d3da58d8f3f>`_]: 1.支持使用内存池类型的Pbuf作为网络数据接收的基本单元(默认使用从堆中分配的Pbuf) 2.menuconfig中增加LwIP内存堆大小，是否使用系统堆算法等配置 3.优化一些LwIP配置
- [`12998353 <http://************/intewell-os/ttos/ttos-n/commit/12998353ef71c486d426c67ba479b33f0e2d63ca>`_]: 解决编译问题
- [`6a729e90 <http://************/intewell-os/ttos/ttos-n/commit/6a729e908bbab299b1bce91dedf3a8252ade85e1>`_]: 已经在排队的信号不做丢弃处理
- [`4c7013c3 <http://************/intewell-os/ttos/ttos-n/commit/4c7013c34b8994b01463f445e1564e31a57065f9>`_]: 修复等到完成量后，等待完成量状态未清理的问题
- [`85de0052 <http://************/intewell-os/ttos/ttos-n/commit/85de0052173589987c6de3ced31560da5d8f4e00>`_]: Merge branch 'zyh' into 'master'
- [`f215df96 <http://************/intewell-os/ttos/ttos-n/commit/f215df96dac9bad8277ec22c628bfd64c9df648c>`_]: 添加b2日志
