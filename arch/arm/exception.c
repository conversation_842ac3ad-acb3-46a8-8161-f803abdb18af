/**
 * @file    arch/arm/exception.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * e880932c 2024-07-23 coredump改为传入所有映射过的用户态地址内容，修复无法bt回溯的问题
 * b182ca5c 2024-07-17 coredump功能支持：在用户态发生异常时保存异常任务现场，并通过gdb查看
 * 914d9323 2024-07-11 添加周期任务支持
 * ac37f7cc 2024-07-01 1. 增加log模式的trace信息 2. 修改部分tracepoint确保关调度时统计
 * 3511844a 2024-07-02 移除include路径中的trace
 * ac006b61 2024-07-02 移除一级ttos目录
 * 340ba810 2024-07-01 修改拼写错误
 * f67a5be3 2024-06-26 1. trace ctf格式编码正常，packet header中增加cpu id 2.
 * 添加中断入口的trace点 3. 优化log格式下文件的写入信息处理 4. 优化文件后端写入处理 71e9846b
 * 2024-06-24 添加栈溢出处理时使用临时栈 4a600b1a 2024-06-20 优化中断控制器处理流程 df703b2a
 * 2024-06-19 1. trace event模板优化 2. 新增跟踪mutex、fork、中断的tracepoint 3.
 * tracing_log.h不再依赖ttos.h头文件 51be2d21 2024-06-17 Trace模块调整syscall的记录格式 79314f75
 * 2024-06-06 Trace在内核中添加模块、系统调用的trace记录点, 优化部分代码 ecc8bdb4 2024-06-12
 * 异常打印中加入对task的判断 避免在启动第一个任务前进入异常时无法打印错误信息 dba221b9 2024-06-06
 * 添加KLOG的紧急消息以提供立即输出 865ecac5 2024-05-23 正确处理进程异常 3823374e 2024-05-23
 * 添加signal功能模块 6fd7a00d 2024-05-22 整理libk并格式化代码 61252be5 2024-05-22 丰富异常信息
 * be1a0b92 2024-05-21 irq命名修改为ttos_pic_xxx, 头文件引用删<irq.h>
 * b041d869 2024-05-15 格式化代码并处理一些头文件依赖问题
 * 82306706 2024-05-10 修复破坏r1寄存器
 * 5d151716 2024-05-09 增加异常时打印栈信息
 * 50f107ed 2024-05-09 文件系统挂载成功
 * ce6e5b00 2024-05-09 更新异常打印的信息和抛异常的顺序
 * 84020ca7 2024-04-29 添加 vfork系统调用
 * fe0becbf 2024-04-28 增加fork系统调用
 * b5bfac3d 2024-04-26 增加TRY CATCH功能
 * 9a1db972 2024-04-25 修复异常不打印问题
 * f4560b75 2024-04-23 更正文件命名
 * 13664d97 2024-04-23 elf运行成功添加临时测试的elf文件
 * edab30a3 2024-04-19 添加backtrace同时解决path中一处错误的指针释放
 * dd26a3f2 2024-04-01 添加系统调用表
 * *************-03-27 移除日志中\n 全部修改为日志输出
 * c7bbbfca 2024-03-18 提交任务功能模块
 * 43b302d7 2024-03-13 添加中断、自旋锁、原子操作相关功能实现。
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

/************************头 文 件******************************/
#include <context.h>
#include <cp15.h>
#include <cpu.h>
#include <cpuid.h>
#include <period_sched_group.h>
#include <process_signal.h>
#include <ptrace/ptrace.h>
#include <signal.h>
#include <stdint.h>
#include <stdio.h>
#include <syscalls.h>
#include <ttos.h>
#include <ttosInterTask.inl>
#include <ttosProcess.h>
#include <ttos_pic.h>
#include <uaccess.h>

#include <trace/tracing.h>
#undef KLOG_TAG
#define KLOG_TAG "Exception"
#include <klog.h>

/************************宏 定 义******************************/
/************************类型定义******************************/
/************************外部声明******************************/
void restore_context(void *context);
extern size_t intNestLevel[CONFIG_MAX_CPUS];
void ttosSchedule(void);
extern syscall_func syscall_table[CONFIG_SYSCALL_NUM];
extern syscall_func syscall_extent_table[CONFIG_EXTENT_SYSCALL_NUM];
void backtrace_r(const char *cookie, uintptr_t frame_address);
extern void kernel_set_tls(uintptr_t tls);
void arch_signal_quit(arch_exception_context_t *context);
extern void ptrace_cancel_bpt(pcb_t pcb);
void do_work_pending(void *exp_frame);

/************************前向声明******************************/
bool stack_overflow(void);
/************************模块变量******************************/
/************************全局变量******************************/
size_t irqstack[CONFIG_MAX_CPUS][8] = {0};
size_t fiqstack[CONFIG_MAX_CPUS][8] = {0};
size_t svcstack[CONFIG_MAX_CPUS][8] = {0};
size_t undefstack[CONFIG_MAX_CPUS][8] = {0};
size_t abortstack[CONFIG_MAX_CPUS][8] = {0};
extern unsigned char tmp_stack[PAGE_SIZE * CONFIG_MAX_CPUS] __attribute__((aligned(PAGE_SIZE)));

struct fsr_info
{
    int sig;
    int code;
    const char *name;
};

/************************函数实现******************************/
static struct fsr_info short_instruction_fault_info[] = {
    [0b00001] = {SIGBUS, BUS_ADRALN, "PC alignment fault."},
    [0b00010] = {SIGBUS, 0, "Debug exception."},
    [0b00011] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 1."},
    [0b00101] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 1."},
    [0b00110] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 2."},
    [0b00111] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 2."},
    [0b01000] = {SIGBUS, 0, "Synchronous External abort, not on translation table walk."},
    [0b01001] = {SIGBUS, 0, "Domain fault, level 1."},
    [0b01011] = {SIGBUS, 0, "Domain fault, level 2."},
    [0b01100] = {SIGBUS, 0, "Synchronous External abort, on translation table walk, level 1."},
    [0b01101] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 1."},
    [0b01110] = {SIGBUS, 0, "Synchronous External abort, on translation table walk, level 2."},
    [0b01111] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 2."},
    [0b10000] = {SIGBUS, 0, "TLB conflict abort."},
    [0b10100] = {SIGBUS, 0, "IMPLEMENTATION DEFINED fault (Lockdown fault)."},
    [0b11001] = {SIGBUS, 0,
                 "Synchronous parity or ECC error on memory access, not on "
                 "translation table walk. When FEAT_RAS is not implemented"},
    [0b11100] = {SIGBUS, 0,
                 "Synchronous parity or ECC error on translation table walk, "
                 "level 1. When FEAT_RAS is not implemented"},
    [0b11110] = {SIGBUS, 0,
                 "Synchronous parity or ECC error on translation table walk, "
                 "level 2. When FEAT_RAS is not implemented"},
};

static struct fsr_info long_instruction_fault_info[] = {
    [0b000000] = {SIGBUS, 0, "Address size fault in translation table base register."},
    [0b000001] = {SIGBUS, 0, "Address size fault, level 1."},
    [0b000010] = {SIGBUS, 0, "Address size fault, level 2."},
    [0b000011] = {SIGBUS, 0, "Address size fault, level 3."},
    [0b000101] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 1."},
    [0b000110] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 2."},
    [0b000111] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 3."},
    [0b001001] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 1."},
    [0b001010] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 2."},
    [0b001011] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 3."},
    [0b001101] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 1."},
    [0b001110] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 2."},
    [0b001111] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 3."},
    [0b010000] = {SIGBUS, 0, "Synchronous External abort, not on translation table walk."},
    [0b010101] = {SIGBUS, 0, "Synchronous External abort on translation table walk, level 1."},
    [0b010110] = {SIGBUS, 0, "Synchronous External abort on translation table walk, level 2."},
    [0b010111] = {SIGBUS, 0, "Synchronous External abort on translation table walk, level 3."},
    [0b011000] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access, not on "
                  "translation table walk. When FEAT_RAS is not implemented"},
    [0b011101] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access on "
                  "translation table "
                  "walk, level 1. When FEAT_RAS is not implemented"},
    [0b011110] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access on "
                  "translation table "
                  "walk, level 2. When FEAT_RAS is not implemented"},
    [0b011111] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access on "
                  "translation table "
                  "walk, level 3. When FEAT_RAS is not implemented"},
    [0b100001] = {SIGBUS, BUS_ADRALN, "PC alignment fault."},
    [0b100010] = {SIGBUS, 0, "Debug exception."},
    [0b110000] = {SIGBUS, 0, "TLB conflict abort."},
};

static struct fsr_info short_data_fault_info[] = {
    [0b00001] = {SIGBUS, BUS_ADRALN, "Alignment fault."},
    [0b00010] = {SIGBUS, 0, "Debug exception."},
    [0b00011] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 1."},
    [0b00100] = {SIGBUS, 0, "Fault on instruction cache maintenance."},
    [0b00101] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 1."},
    [0b00110] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 2."},
    [0b00111] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 2."},
    [0b01000] = {SIGBUS, 0, "Synchronous External abort, not on translation table walk."},
    [0b01001] = {SIGBUS, 0, "Domain fault, level 1."},
    [0b01011] = {SIGBUS, 0, "Domain fault, level 2."},
    [0b01100] = {SIGBUS, 0, "Synchronous External abort, on translation table walk, level 1."},
    [0b01101] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 1."},
    [0b01110] = {SIGBUS, 0, "Synchronous External abort, on translation table walk, level 2."},
    [0b01111] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 2."},
    [0b10000] = {SIGBUS, 0, "TLB conflict abort."},
    [0b10100] = {SIGBUS, 0, "IMPLEMENTATION DEFINED fault (Lockdown fault)."},
    [0b10101] = {SIGBUS, 0, "IMPLEMENTATION DEFINED fault (Unsupported Exclusive access fault)."},
    [0b10110] = {SIGBUS, 0, "SError interrupt."},
    [0b11000] = {SIGBUS, 0,
                 "SError interrupt, from a parity or ECC error on memory "
                 "access. When FEAT_RAS is not implemented"},
    [0b11001] = {SIGBUS, 0,
                 "Synchronous parity or ECC error on memory access, not on "
                 "translation table walk. When FEAT_RAS is not implemented"},
    [0b11100] = {SIGBUS, 0,
                 "Synchronous parity or ECC error on translation table walk, "
                 "level 1. When FEAT_RAS is not implemented"},
    [0b11110] = {SIGBUS, 0, "Synchronous parity or ECC error on translation table walk, level 2."},
};

static struct fsr_info long_data_fault_info[] = {
    [0b000000] = {SIGBUS, 0, "Address size fault in translation table base register."},
    [0b000001] = {SIGBUS, 0, "Address size fault, level 1."},
    [0b000010] = {SIGBUS, 0, "Address size fault, level 2."},
    [0b000011] = {SIGBUS, 0, "Address size fault, level 3."},
    [0b000101] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 1."},
    [0b000110] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 2."},
    [0b000111] = {SIGSEGV, SEGV_MAPERR, "Translation fault, level 3."},
    [0b001001] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 1."},
    [0b001010] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 2."},
    [0b001011] = {SIGSEGV, SEGV_ACCERR, "Access flag fault, level 3."},
    [0b001101] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 1."},
    [0b001110] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 2."},
    [0b001111] = {SIGSEGV, SEGV_ACCERR, "Permission fault, level 3."},
    [0b010000] = {SIGBUS, 0, "Synchronous External abort, not on translation table walk."},
    [0b010001] = {SIGBUS, 0, "Asynchronous SError interrupt."},
    [0b010101] = {SIGBUS, 0, "Synchronous External abort on translation table walk, level 1."},
    [0b010110] = {SIGBUS, 0, "Synchronous External abort on translation table walk, level 2."},
    [0b010111] = {SIGBUS, 0, "Synchronous External abort on translation table walk, level 3."},
    [0b011000] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access, not on "
                  "translation table walk. When FEAT_RAS is not implemented"},
    [0b011001] = {SIGBUS, 0,
                  "Asynchronous SError interrupt, from a parity or ECC error on "
                  "memory access. When FEAT_RAS is not implemented"},
    [0b011101] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access on translation table "
                  "walk, level 1. When FEAT_RAS is not implemented"},
    [0b011110] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access on translation table "
                  "walk, level 2. When FEAT_RAS is not implemented"},
    [0b011111] = {SIGBUS, 0,
                  "Synchronous parity or ECC error on memory access on translation table "
                  "walk, level 3. When FEAT_RAS is not implemented"},
    [0b100001] = {SIGBUS, BUS_ADRALN, "Alignment fault."},
    [0b100010] = {SIGBUS, 0, "Debug exception."},
    [0b110000] = {SIGBUS, 0, "TLB conflict abort."},
    [0b110100] = {SIGBUS, 0, "IMPLEMENTATION DEFINED fault (Lockdown)."},
    [0b110101] = {SIGBUS, 0, "IMPLEMENTATION DEFINED fault (Unsupported Exclusive access)."},
};
/**
 * @brief
 *    设置该上下文类型是否是系统调用上下文
 * @param[in] context 上下文
 * @retval 无
 */
void set_context_type(struct arch_context *context, int type)
{
    context->type = type;
    context->ori_r0 = context->r0;
}

/**
 * @brief
 *    上下文show
 * @param[in] context 上下文
 * @retval 无
 */
#define BREAKINST_ARM 0xe7f001f0

static void save_exce_context(pcb_t pcb, struct arch_context *context)
{
    memcpy(&pcb->exception_context, context, sizeof(pcb->exception_context));
}

/* 检查用户态上下文是否被破坏 */
static inline bool user_context_valid(struct arch_context *context)
{
    if (!user_access_check((void *)context->sp, sizeof(context->sp), UACCESS_RW))
        return false;

    if (!user_access_check((void *)context->pc, sizeof(context->pc), UACCESS_R))
        return false;

    return true;
}

void do_exception(struct arch_context *context)
{
    struct fsr_info *info = NULL;
    TASK_ID task = ttosGetRunningTask();
    pcb_t pcb = NULL;
    uint32_t dfsr = sysreg_read(DFSR);
    uint32_t ifsr = sysreg_read(IFSR);

    if (GET_M32(context->cpsr) == MODE32_USR && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    /* 识别break poing exception */
    if (kernel_access_check((void *)(context->pc - 4), 4, UACCESS_R))
    {
        uint32_t break_instr = *(uint32_t *)(context->pc - 4);

        if (BREAKINST_ARM == break_instr)
        {
            ksiginfo_t siginfo;

            siginfo.si_addr = (void *)sysreg_read(IFAR);
            siginfo.si_code = 0x0004;
            if (pcb != NULL)
            {
                if (pcb->group_leader->ptrace & PT_SINGLESTEP)
                {
                    /* 取消所有断点 */
                    ptrace_cancel_bpt(pcb);
                }
                /* 向当前进程发送SIGTRAP信号 */
                kernel_signal_kill(task->tid, TO_THREAD, SIGTRAP, siginfo.si_code, &siginfo);

                /* 恢复上下文会做信号检测 */
                if (pcb->group_leader->ptrace)
                {
                    pcb->exception_context.pc -= 4;
                }

                set_context_type(&pcb->exception_context, EXCEPTION_CONTEXT);
                restore_context(&pcb->exception_context);
            }
        }
    }

    if (GET_M32(context->cpsr) == MODE32_USR)
    {
        KLOG_EMERG("================User Exception================");
    }
    else
    {
        KLOG_EMERG("================Kernel Exception================");
    }

    if (dfsr != 0)
    {
        int is_long;

        KLOG_EMERG("Data Fault Exception DFSR: 0x%X Fault Address: %p", dfsr,
                   (void *)sysreg_read(DFAR));
        is_long = !!(dfsr & (0x01 << 9));
        if (is_long)
        {
            KLOG_EMERG("Using the Long-descriptor translation table formats.");
            info = &long_data_fault_info[(dfsr & 0x3f)];
        }
        else
        {
            KLOG_EMERG("Using the Short-descriptor translation table formats.");
            info = &short_data_fault_info[(dfsr & 0xf)];
        }

        KLOG_EMERG("Abort caused by a %s instruction.", !!(dfsr & (0x01 << 11)) ? "write" : "read");
        KLOG_EMERG("Reason: %s", info->name);
    }
    if (ifsr != 0)
    {
        int is_long;

        KLOG_EMERG("Instruction Fault Exception IFSR: 0x%X Fault Address: %p", ifsr,
                   (void *)sysreg_read(IFAR));
        is_long = !!(ifsr & (0x01 << 9));
        if (is_long)
        {
            KLOG_EMERG("Using the Long-descriptor translation table formats.");
            info = &long_instruction_fault_info[(dfsr & 0x3f)];
        }
        else
        {
            KLOG_EMERG("Using the Short-descriptor translation table formats.");
            info = &short_instruction_fault_info[(dfsr & 0xf)];
        }

        KLOG_EMERG("Reason: %s", info->name);
    }
    if (task)
    {
        KLOG_EMERG("Exception Task: %s on CPU[%d]", task->objCore.objName, cpuid_get());

        /* 检测上下文是否在临时栈中，如果是栈溢出，则会使用临时栈来保存上下文 */
        if ((unsigned long)context >= (unsigned long)tmp_stack &&
            (unsigned long)context <= ((unsigned long)tmp_stack + sizeof(tmp_stack)))
        {
            KLOG_EMERG("Stack OverFlow");
            KLOG_EMERG("OverFlow sp:%p", (void *)context->err_sp);
            KLOG_EMERG("Kernel Stack: %p-%p", task->stackStart, task->kernelStackTop);
        }
    }

    KLOG_EMERG("RegMap:");
    KLOG_EMERG("r0     :%p", (void *)context->r0);
    KLOG_EMERG("r1     :%p", (void *)context->r1);
    KLOG_EMERG("r2     :%p", (void *)context->r2);
    KLOG_EMERG("r3     :%p", (void *)context->r3);
    KLOG_EMERG("r4     :%p", (void *)context->r4);
    KLOG_EMERG("r5     :%p", (void *)context->r5);
    KLOG_EMERG("r6     :%p", (void *)context->r6);
    KLOG_EMERG("r7     :%p", (void *)context->r7);
    KLOG_EMERG("r8     :%p", (void *)context->r8);
    KLOG_EMERG("r9     :%p", (void *)context->r9);
    KLOG_EMERG("r10(sl):%p", (void *)context->sl);
    KLOG_EMERG("r11(fp):%p", (void *)context->fp);
    KLOG_EMERG("r12(ip):%p", (void *)context->ip);
    KLOG_EMERG("r13(sp):%p", (void *)context->sp);
    KLOG_EMERG("r14(lr):%p", (void *)context->lr);
    KLOG_EMERG("r15(pc):%p", (void *)context->pc);
    KLOG_EMERG("cpsr   :%p", (void *)context->cpsr);
    backtrace_r("unwind", context->fp);
    if (task)
    {
        if (GET_M32(context->cpsr) == MODE32_USR && task->ppcb && info)
        {
            pcb_t pcb = (pcb_t)task->ppcb;
#ifdef CONFIG_COREDUMP
            save_exce_context(pcb, context);
#endif
            pid_t pid = get_process_pid((pcb_t)task->ppcb);
            kernel_signal_kill(pid, TO_PROCESS, info->sig, info->code, NULL);
            set_context_type(context, EXCEPTION_CONTEXT);

            /* 避免返回到用户态的上下文信息被破坏 */
            if (user_context_valid(context))
            {
                restore_context(context);
            }
            else
            {
                /* 当上下文信息被破坏时 不再返回用户态
                 * 但是应该检查信号以便进行coredump处理或者进程退出 */
                do_work_pending(context);
            }
        }

        TTOS_SuspendTask(task);
    }

    while (1)
        ;
}

/**
 * @brief
 *    中断处理程序
 * @param[in] context 中断上下文
 * @retval 无
 */
void do_irq(arch_int_context_t *context)
{
    s32 ret;
    uint32_t from_cpu;
    uint32_t irq = 0;
    s32 cpuid = 0;
    TASK_ID task = ttosGetRunningTask();
    pcb_t pcb = NULL;

    if (GET_M32(context->cpsr) == MODE32_USR && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    cpuid = cpuid_get();

    ret = ttos_pic_irq_ack(&from_cpu, &irq);
    if (ret == 0)
    {
        // KLOG_I("irq happend irq:%d from cpu:%d", irq, from_cpu);
        TRACING_EVENT_ENTER(isr, irq, from_cpu);

        ttosDisableScheduleLevel[cpuid]++;
        intNestLevel[cpuid]++;

        ttos_pic_irq_handle(irq);

        ttos_pic_irq_eoi(irq, from_cpu);

        intNestLevel[cpuid]--;
        ttosDisableScheduleLevel[cpuid]--;

        TRACING_EVENT_EXIT(isr, irq);
    }

    ttosSchedule();

    set_context_type(context, IRQ_CONTEXT);
    restore_context(context);

    while (1)
        ;
}

/**
 * @brief
 *    data abort处理程序
 * @param[in] context 异常上下文
 * @retval 无
 */
void do_data_abort(arch_exception_context_t *context)
{
    do_exception(context);
}

/**
 * @brief
 *    prefetch abort处理程序
 * @param[in] context 异常上下文
 * @retval 无
 */
void do_prefetch_abort(arch_exception_context_t *context)
{
    do_exception(context);
}

/**
 * @brief
 *    调用中断处理程序
 * @param[in] context 异常上下文
 * @retval 无
 */

void do_undefine(arch_exception_context_t *context)
{
    do_exception(context);
}

/**
 * @brief
 *    syscall处理程序
 * @param[in] context 系统调用上下文
 * @retval 无
 */
void do_syscall(arch_exception_context_t *context)
{
    long long ret = 0;
    int syscall_num = 0;

    TASK_ID task = ttosGetRunningTask();
    pcb_t pcb = NULL;

    if (GET_M32(context->cpsr) == MODE32_USR && task && task->ppcb)
    {
        pcb = (pcb_t)task->ppcb;
        save_exce_context(pcb, context);
        TTOS_TaskEnterKernelHook(task);
    }

    syscall_num = context->r7;

    set_context_type(context, SYSCALL_CONTEXT);

    arch_cpu_int_enable();

    if (is_extent_syscall_num(syscall_num))
    {
        syscall_num -= CONFIG_EXTENT_SYSCALL_NUM_START;
        if (0 == syscall_num)
        {
            ret = syscall_extent_table[syscall_num]((long)context, 0, 0, 0, 0, 0);
        }
        else
        {
            ret = syscall_extent_table[syscall_num](context->r0, context->r1, context->r2,
                                                    context->r3, context->r4, context->r5);
        }

        context->r0 = ret & 0xffffffff;
        restore_context(context);
    }

    if ((syscall_num >= CONFIG_SYSCALL_NUM) && (syscall_num != __ARM_NR_set_tls))
    {
        KLOG_I("syscall num %d great than:%d\n", syscall_num, CONFIG_SYSCALL_NUM - 1);
    }
    else
    {
        if (syscall_num == __ARM_NR_set_tls)
        {
            TRACING_EVENT_ENTER(syscall, syscall_num, "set_tls", context->r0, 0, 0, 0, 0, 0);

            kernel_set_tls(context->r0);
            context->r0 = 0;
            TRACING_EVENT_EXIT(syscall, "set_tls", context->r0);
        }
        else if (syscall_num == __NR_rt_sigreturn || syscall_num == __NR_sigreturn)
        {
            /* 信号hander调用完毕的返回系统调用 */
            rt_sigreturn(context);
        }
        else if (syscall_table[syscall_num])
        {
            TRACING_EVENT_ENTER(syscall, syscall_num, syscall_getname(syscall_num), context->r0,
                                context->r1, context->r2, context->r3, context->r4, context->r5);

            ret = syscall_table[syscall_num](context->r0, context->r1, context->r2, context->r3,
                                             context->r4, context->r5);

            context->r0 = ret & 0xffffffff;
            // KLOG_E("[%d]SysCall: %s(%d) ret: %d()", ttosProcessSelf() == NULL ? -1 :
            // get_process_pid(ttosProcessSelf()), syscall_getname (syscall_num), syscall_num, ret,
            // ret < 0 ? strerror(-ret) : strerror(0));
            TRACING_EVENT_EXIT(syscall, syscall_getname(syscall_num), context->r0);
        }
        else
        {
            KLOG_E("syscall_table[%d] is NULL\n", syscall_num);
        }
    }

    restore_context(context);
}

bool stack_overflow(void)
{
    unsigned long sp_val = 0;

    asm volatile("mov %0, sp" : "=r"(sp_val)::"memory");

    T_TTOS_TaskControlBlock *task = ttosGetCurrentCpuRunningTask();

    /* 暂且认为只差128字节就要溢出也算溢出 */
    if (sp_val < (unsigned long)task->stackBottom + 128)
    {
        KLOG_E("stack_overflow happend, overflow sp:%p!!!", (void *)sp_val);
        KLOG_E("task:%s stack %p---%p !!!", task->objCore.objName, task->stackBottom,
               task->kernelStackTop);
        return true;
    }
    else
    {
        return false;
    }
}
