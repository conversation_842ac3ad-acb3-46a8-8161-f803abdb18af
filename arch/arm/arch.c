#include <ttos_arch.h>
#include <cpu.h>
#include <ttosProcess.h>

void do_work_pending(void *exp_frame);
void restore_raw_context(arch_exception_context_t *context);

void arch_switch_context_set_stack(T_TBSP_TaskContext *ctx, long sp)
{
    ctx->sp = sp;
}

void arch_context_set_return (arch_exception_context_t *context, long value)
{
    context->r0 = value;
}

void arch_context_set_stack (arch_exception_context_t *context, long value)
{
    context->sp = value;
}

long arch_context_get_args (arch_exception_context_t *context, int index)
{
    switch (index)
    {
    case 0:
        return context->r0;
    case 1:
        return context->r1;
    case 2:
        return context->r2;
    case 3:
        return context->r3;
    case 4:
        return context->r4;
    case 5:
        return context->r5;
    case 6:
        return context->r6;
    case 7:
        return context->r7;
    case 8:
        return context->r8;
    case 9:
        return context->r9;
    default:
        break;
    }
    return -1;
}

long arch_context_thread_init (arch_exception_context_t *context)
{

    return 0;
}

void restore_context(arch_exception_context_t *context)
{
    if((context->cpsr & MODE32_MASK) == MODE32_USR)
    {
        do_work_pending(context);

        pcb_t pcb = ttosProcessSelf();
        if(pcb_get(pcb))
        {
#if 0
            if (pcb->group_leader->ptrace)
            {
                restore_hw_debug(pcb->group_leader);
            }
#endif
            TTOS_TaskEnterUserHook(pcb->taskControlId);
            pcb_put(pcb);
        }
    }

    restore_raw_context(context);
}
