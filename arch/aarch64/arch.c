#include <ttos_arch.h>
#include <cpu.h>
#include <ttos.h>
#include <ttosProcess.h>

void arch_switch_context_set_stack(T_TBSP_TaskContext *ctx, long sp)
{
    ctx->sp = sp;
}

void arch_context_set_return (arch_exception_context_t *context, long value)
{
    context->regs[0] = value;
}

void arch_context_set_stack (arch_exception_context_t *context, long value)
{
    context->sp = value;
}


long arch_context_thread_init (arch_exception_context_t *context)
{
    return 0;
}

long arch_context_get_args (arch_exception_context_t *context, int index)
{
    if(index <= 31)
    {
        return context->regs[index];
    }
    return -1;
}
void do_work_pending(void *exp_frame);
void restore_raw_context(arch_exception_context_t *context);
void restore_hw_debug(pcb_t pcb);
void restore_context(arch_exception_context_t *context)
{
    
    if (GET_EL (context->cpsr) == MODE_EL0)
    {
        do_work_pending(context);

        pcb_t pcb = ttosProcessSelf();
        if(pcb_get(pcb))
        {
            if (pcb->group_leader->ptrace)
            {
                restore_hw_debug(pcb->group_leader);
            }
            TTOS_TaskEnterUserHook(pcb->taskControlId);
            pcb_put(pcb);
        }
        
    }
    restore_raw_context(context);
}